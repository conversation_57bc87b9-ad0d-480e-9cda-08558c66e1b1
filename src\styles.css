/**
 * Основной файл стилей для сайта Telegram Support Bot
 * Содержит все стили для сайта, включая светлую и темную темы
 * <AUTHOR> Agent
 * @version 1.0.0
 */

/* Импортируем базовые стили Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/**
 * Основные стили для HTML и предотвращение мигания при загрузке
 * Настройки для плавной прокрутки и предотвращения скачков
 */
html {
  @apply transition-none;
  scroll-behavior: smooth;       /* Плавная прокрутка страницы */
  overflow-y: scroll;            /* Всегда показывать полосу прокрутки */
  overflow-x: hidden;            /* Предотвращает горизонтальный скролл */
  overscroll-behavior: none;     /* Предотвращает нежелательные скачки при прокрутке */
  max-width: 100%;               /* Ограничивает ширину содержимого */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Скрываем страницу до применения темы */
html.preload * {
  transition: none !important;
}

/* Стили для темной темы */
html.dark {
  background-color: #0a1428;
  color: #fff;
}

/* Дополнительные стили для темной темы */
html.dark .section-title h2 {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

html.dark .download-icon i {
  filter: drop-shadow(0 2px 4px rgba(0, 102, 255, 0.3));
}

/* Стили для светлой темы */
html:not(.dark) {
  background-color: #f8fafc;
  color: #000;
}

/* Для браузеров, поддерживающих вариативные шрифты */
@supports (font-variation-settings: normal) {
  html {
    font-family: 'Inter var', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }
}

/**
 * Класс loaded добавляется после загрузки страницы для включения плавных переходов
 */
html.loaded {
  @apply transition-colors duration-300; /* Плавный переход между темами */
}

/**
 * Скрытие содержимого до загрузки JavaScript
 * Предотвращает мигание и некорректное отображение страницы
 */
html.no-js body {
  @apply invisible; /* Скрываем содержимое, если JavaScript отключен */
}

html:not(.no-js) body {
  @apply visible; /* Показываем содержимое, если JavaScript включен */
}

/**
 * Базовые стили для элементов страницы
 * Основные настройки для тела страницы и других базовых элементов
 */
@layer base {
  /**
   * Стили для тела страницы
   * Включают цвета фона и текста для светлой и темной темы
   */
  body {
    @apply bg-light text-dark dark:bg-dark-dark dark:text-light transition-colors duration-300;
    overscroll-behavior-y: none; /* Предотвращает скачки при прокрутке */
    position: relative;          /* Для правильного позиционирования элементов */
    min-height: 100vh;           /* Минимальная высота страницы на весь экран */
    overflow-x: hidden;          /* Предотвращает горизонтальный скролл */
    width: 100%;                 /* Занимает всю доступную ширину */
  }
}

/**
 * Компоненты и многократно используемые элементы интерфейса
 * Стили для кнопок, карточек, контейнеров и других повторяющихся элементов
 */
@layer components {
  /**
   * Контейнер - основной элемент для ограничения ширины контента
   * Используется для центрирования и ограничения максимальной ширины контента
   */
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8; /* Адаптивные отступы для разных размеров экрана */
  }

  /* Стили для кнопок перенесены в общий раздел стилей */

  /**
   * Специальный стиль для кнопок в тарифных планах
   */
  .pricing-btn {
    @apply w-full justify-center flex items-center;
  }

  .pricing-card.popular .pricing-btn {
    @apply bg-secondary dark:bg-blue-600 hover:bg-primary dark:hover:bg-primary-dark;
  }

  .section {
    @apply py-24;
  }

  .section-title {
    @apply text-center mb-16;
  }

  .section-title h2 {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold text-secondary dark:text-light mb-4 tracking-tight;
  }

  .section-title p {
    @apply text-base md:text-lg text-gray-800 dark:text-gray-100 max-w-2xl mx-auto leading-relaxed;
  }

  .feature-card {
    @apply bg-white dark:bg-dark-dark rounded-xl p-8 shadow-md transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-xl dark:shadow-black/30 border border-gray-100 dark:border-gray-800;
  }

  .feature-icon {
    @apply text-4xl text-primary mb-6;
  }

  .feature-card h3 {
    @apply text-xl md:text-2xl font-semibold text-secondary dark:text-light mb-4 tracking-tight;
  }

  .feature-card p {
    @apply text-base leading-relaxed;
  }

  .screenshot-item {
    @apply rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:scale-[1.02] cursor-pointer dark:shadow-black/30 flex flex-col h-full border border-gray-100 dark:border-gray-800;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
  }

  .screenshot-item:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  }

  .dark .screenshot-item {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
  }

  .dark .screenshot-item:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  }

  .screenshot-image-container {
    position: relative;
    overflow: hidden;
  }

  .screenshot-image {
    @apply w-full h-full transition-all duration-300;
    object-fit: cover;
    object-position: center top;
  }

  .screenshot-item:hover .screenshot-image {
    @apply brightness-105;
  }

  .screenshot-caption {
    @apply p-4 h-16 bg-white dark:bg-dark-dark text-center font-medium text-dark dark:text-light flex items-center justify-center text-sm md:text-base border-t border-gray-100 dark:border-gray-800 tracking-tight;
  }

  /**
   * Стили для тарифных планов
   * Карточки с тарифами и их стилизация
   */
  .pricing-card {
    @apply bg-white dark:bg-dark-dark rounded-xl p-6 md:p-8 shadow-md text-center transition-all duration-300 hover:-translate-y-2 hover:shadow-lg dark:shadow-black/30 border border-gray-100 dark:border-gray-800 h-full;
  }

  /* Базовый тариф с более яркой тенью в светлой теме */
  .pricing-card:first-child {
    @apply shadow-[0_4px_20px_rgba(0,102,255,0.15)] hover:shadow-[0_8px_30px_rgba(0,102,255,0.25)];
  }

  .pricing-card.popular {
    @apply border-2 border-primary dark:border-primary-dark relative transform scale-[1.02] z-10;
  }

  .popular-tag {
    @apply absolute -top-4 left-1/2 transform -translate-x-1/2 bg-primary dark:bg-primary-dark text-white py-1 px-6 rounded-full text-sm font-semibold shadow-md;
  }

  .pricing-card h3 {
    @apply text-2xl md:text-3xl font-semibold text-secondary dark:text-light mb-4 tracking-tight;
  }

  .price {
    @apply text-4xl md:text-5xl font-bold text-primary dark:text-primary-dark mb-6 tracking-tight;
  }

  .price span {
    @apply text-base md:text-lg font-normal text-gray-600 dark:text-gray-300;
  }

  /**
   * Стили для дополнительной информации о ценах
   */
  .price-saving {
    @apply text-sm font-medium text-green-600 dark:text-green-400 mb-6 bg-green-50 dark:bg-green-900/20 py-1 px-3 rounded-full inline-block;
  }

  .price-note {
    @apply text-xs text-gray-500 dark:text-gray-400 mb-2 italic;
  }

  .price-info {
    @apply text-xs text-blue-600 dark:text-blue-400 mb-6 bg-blue-50 dark:bg-blue-900/20 py-1 px-3 rounded-md;
  }

  .price-info-secondary {
    @apply text-sm font-medium text-green-600 dark:text-green-400 mb-6 bg-green-50 dark:bg-green-900/20 py-1 px-3 rounded-full inline-block;
  }

  .price-total {
    @apply text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 bg-gray-50 dark:bg-gray-800/50 py-1 px-3 rounded-full inline-block;
  }

  /**
   * Анимация вращения для иконки обновления
   */
  .animate-spin-slow {
    @apply animate-spin;
    animation-duration: 3s;
  }

  /**
   * Стили для списка функций в тарифных планах
   */
  .pricing-features {
    @apply list-none mb-8 text-left;
  }

  .pricing-features li {
    @apply py-2 border-b border-gray-100 dark:border-gray-800 flex items-center text-base leading-relaxed;
  }

  .pricing-features li::before {
    @apply content-['\2713'] mr-2 text-primary dark:text-primary-dark font-bold;
  }

  /**
   * Стили для переключателя вариантов оплаты и периодов подписки
   */
  .subscription-options {
    @apply flex flex-col items-center justify-center;
  }

  /* Стили для контейнера кнопок периодов */
  .period-buttons-container {
    @apply flex justify-center pt-4;
  }

  .period-buttons-wrapper {
    @apply flex flex-nowrap;
    /* Убран overflow: hidden, чтобы метка не обрезалась */
    border: 2.5px solid #0066ff;
    border-radius: 8px;
  }

  .dark .period-buttons-wrapper {
    border-color: #3b82f6;
  }

  /* Стили для кнопок выбора периода */
  .period-btn {
    @apply py-2 px-3 text-sm font-medium transition-all duration-200 bg-white dark:bg-dark-dark text-gray-700 dark:text-gray-300 border-0 rounded-none;
  }

  /* Скругление для первой и последней кнопки */
  .period-buttons-wrapper > button:first-child,
  .period-buttons-wrapper > div:first-child button {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
  }

  .period-buttons-wrapper > button:last-child,
  .period-buttons-wrapper > div:last-child button {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
  }

  /* Стили для разделителей между кнопками */
  .period-buttons-wrapper > button:not(:last-child),
  .period-buttons-wrapper > div:not(:last-child) {
    @apply border-r border-primary/20 dark:border-primary-dark/20;
  }

  /* Стили для популярного периода */
  .popular-period-container {
    @apply relative;
  }

  .popular-label {
    @apply absolute left-1/2 transform -translate-x-1/2 bg-primary dark:bg-primary-dark text-white text-xs py-0.5 px-2 rounded-full font-medium whitespace-nowrap;
    z-index: 20; /* Увеличиваем z-index, чтобы метка была поверх всего */
    top: -12px; /* Поднимаем метку выше */
  }

  .period-btn:hover {
    @apply text-primary dark:text-white bg-primary/10 dark:bg-primary-dark/30;
  }

  .period-btn.active {
    @apply bg-primary dark:bg-primary-dark text-white font-semibold;
  }

  /* Стили для вариантов периодов */
  .period-option {
    @apply hidden;
  }

  .period-option.active {
    @apply block;
  }

  /* Стили для вариантов оплаты */
  .payment-option {
    @apply hidden;
  }

  .payment-option.active {
    @apply block;
  }

  /* Стили для переключателя вариантов размещения */
  .payment-toggle {
    @apply flex items-center justify-center mt-4;
  }

  .payment-toggle-label {
    @apply text-sm text-gray-600 dark:text-gray-400 cursor-pointer;
  }

  .payment-toggle-label.active {
    @apply text-primary dark:text-primary-dark font-medium;
  }

  .payment-toggle-switch {
    @apply relative inline-block w-12 h-6 mx-3;
  }

  .payment-toggle-switch input {
    @apply opacity-0 w-0 h-0;
  }

  .payment-toggle-slider {
    @apply absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 dark:bg-gray-700 rounded-full transition-all duration-300;
  }

  .payment-toggle-slider:before {
    @apply absolute content-[''] h-4 w-4 left-1 bottom-1 bg-white rounded-full transition-all duration-300;
  }

  input:checked + .payment-toggle-slider {
    @apply bg-primary dark:bg-primary-dark;
  }

  input:checked + .payment-toggle-slider:before {
    @apply transform translate-x-6;
  }

  /* Переключатель темы */
  .theme-switch {
    @apply relative inline-block w-14 h-7 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-400 dark:focus:ring-blue-600;
  }

  .theme-switch-track {
    @apply block w-full h-full bg-gray-200 dark:bg-[rgb(18,30,54)] rounded-full overflow-hidden transition-colors duration-300;
  }

  .theme-switch-thumb {
    @apply absolute top-1 left-1 w-5 h-5 rounded-full shadow-md flex items-center justify-center transition-transform duration-300 ease-in-out;
    --tw-text-opacity: 1;
    background-color: rgb(0 102 255 / var(--tw-text-opacity));
  }

  .dark .theme-switch-thumb {
    @apply transform translate-x-7;
    background-color: rgb(18 30 54 / var(--tw-text-opacity));
  }

  .theme-switch-icon-sun {
    @apply text-yellow-500 block dark:hidden;
  }

  .theme-switch-icon-moon {
    @apply text-yellow-300 hidden dark:block;
  }

  /* Мобильное меню */
  .nav-links {
    @apply transition-all duration-300 ease-in-out transform;
  }

  .nav-links.active {
    @apply flex flex-col fixed top-16 left-0 w-full bg-white dark:bg-dark shadow-md py-4 space-y-4 z-50;
  }

  /* Улучшенные стили для мобильного меню */
  /**
   * Стили для мобильного меню
   * Современный дизайн с иконками и плавными эффектами
   */
  .mobile-menu-container {
    @apply max-h-0 overflow-hidden;
  }

  .mobile-menu-container.flex {
    @apply max-h-screen;
  }

  .mobile-menu-item {
    @apply flex items-center py-3 px-4 rounded-lg font-medium text-dark dark:text-light transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-primary dark:hover:text-primary-dark transform hover:translate-x-1 text-base tracking-tight;
  }

  /**
   * Анимация иконки мобильного меню
   * Плавное превращение из гамбургера в крестик
   */
  .mobile-menu-button {
    @apply relative z-50;
  }

  .mobile-menu-button svg {
    @apply transition-transform duration-300;
  }

  .nav-open .mobile-menu-button svg {
    @apply rotate-90;
  }

  /* Стили для секции "Почему мы" */
  #why-us .flex {
    max-width: 100%;
    overflow-x: hidden;
  }

  #why-us p {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    @apply text-base leading-relaxed;
    margin-bottom: 0;
  }

  #why-us h4 {
    @apply tracking-tight;
    margin-top: 0;
  }

  /* Стили для иконок в секции "Почему мы" */
  #why-us .icon-container {
    margin-right: 1rem;
    flex-shrink: 0;
  }

  #why-us .icon-wrapper {
    position: relative;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
    background-color: rgba(0, 102, 255, 0.03);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .dark #why-us .icon-wrapper {
    background-color: rgba(59, 130, 246, 0.05);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  }

  #why-us .icon-light,
  #why-us .icon-dark {
    width: 56px;
    height: 56px;
    transition: all 0.3s ease;
    object-fit: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  #why-us .icon-light {
    display: block;
  }

  #why-us .icon-dark {
    display: none;
  }

  .dark #why-us .icon-light {
    display: none;
  }

  .dark #why-us .icon-dark {
    display: block;
  }

  #why-us .flex-grow {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  #why-us .feature-item {
    display: flex;
    align-items: center;
    min-height: 120px;
    overflow: hidden;
    margin-bottom: 1.5rem;
  }

  #why-us .feature-item:last-child {
    margin-bottom: 0;
  }

  #why-us .feature-item > div:last-child {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  #why-us .icon-wrapper:hover {
    background-color: rgba(0, 102, 255, 0.08);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  #why-us .icon-wrapper:hover .icon-light,
  #why-us .icon-wrapper:hover .icon-dark {
    transform: translate(-50%, -50%) scale(1.05);
  }

  .dark #why-us .icon-wrapper:hover {
    background-color: rgba(59, 130, 246, 0.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  }

  /* Специальные стили для секции скриншотов */
  #screenshots .container {
    max-width: 1400px;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  #screenshots .grid {
    @apply gap-5;
  }

  /* Специальные стили для секции возможностей */
  @media (min-width: 640px) and (max-width: 1023px) {
    #features .grid {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  /* Улучшения для мобильных устройств */
  @media (max-width: 640px) {
    html, body {
      overscroll-behavior-y: none;
      -webkit-overflow-scrolling: touch; /* Для iOS */
      overflow-x: hidden;
    }

    .container {
      @apply px-4;
      max-width: 100vw;
      overflow-x: hidden;
    }

    .section {
      @apply py-16;
      overflow-x: hidden;
    }

    .section-title h2 {
      @apply text-2xl mb-3;
    }

    .section-title {
      @apply mb-10;
    }

    /* Дополнительные стили для секции "Почему мы" на мобильных устройствах */
    #why-us .flex {
      padding-left: 0;
      padding-right: 0;
    }

    #why-us .flex-shrink-0 {
      min-width: 3rem;
    }

    /* Стили для секции скриншотов на мобильных устройствах */
    #screenshots .grid {
      @apply gap-3;
    }

    .screenshot-item {
      @apply mb-2;
    }

    .screenshot-item > div {
      @apply h-64;
    }
  }

  /* Анимации */
  .animate-fade-in {
    @apply animate-fade-in;
  }

  .animate-slide-up {
    @apply animate-slide-up;
  }

  .animate-slide-down {
    @apply animate-slide-down;
  }

  /* Стили для модальной галереи */
  .fade-in {
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
  }

  .fade-out {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  /* Стили для индикатора галереи */
  .gallery-dot {
    @apply w-2 h-2 rounded-full bg-gray-400 transition-all duration-300;
  }

  .gallery-dot.active {
    @apply bg-white w-3 h-3;
  }

  /* Стили для зума изображений */
  #imageContainer {
    transition: all 0.3s ease-out;
    cursor: zoom-in;
    position: relative;
    max-width: 100%;
    margin: 0 auto;
    touch-action: none; /* Отключаем стандартные жесты браузера */
    -webkit-user-select: none; /* Предотвращаем выделение текста */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  #imageContainer.zoomed {
    cursor: zoom-out;
    overflow: hidden;
  }

  #imageContainer img {
    display: block;
    max-width: 100%;
    max-height: 80vh;
    margin: 0 auto;
    transition: transform 0.3s ease-out;
    touch-action: none; /* Отключаем стандартные жесты браузера */
    -webkit-touch-callout: none; /* Предотвращаем контекстное меню на iOS */
  }

  #imageContainer.zoomed img {
    will-change: transform;
    transform-origin: center; /* По умолчанию трансформация от центра */
  }

  /* Стили для мобильных устройств */
  @media (max-width: 768px) {
    #imageContainer {
      touch-action: none;
    }

    #imageModal {
      -webkit-overflow-scrolling: touch; /* Для плавной прокрутки на iOS */
    }

    /* Улучшенная кнопка закрытия для мобильных устройств */
    #closeGalleryBtn {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      z-index: 60; /* Повышенный z-index для предотвращения перекрытия */
      -webkit-tap-highlight-color: transparent; /* Убираем подсветку при тапе на iOS */
    }
  }

  /* Стили для подсказок в галерее */
  .gallery-hint {
    animation: fadeInUp 0.5s ease-out 0.3s both;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 0.7;
      transform: translateY(0);
    }
  }
}

/* Стили для мобильного меню */
@media (max-width: 768px) {
  .mobile-menu-container {
    position: fixed !important;
    top: 80px !important;
  }

  header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background-color: inherit;
  }

  main {
    padding-top: 90px;
  }
}

/**
 * Стили для секции "Кому это подходит"
 */
.business-card {
  @apply bg-white dark:bg-dark-dark rounded-xl p-6 shadow-md transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-xl dark:shadow-black/30 border border-gray-100 dark:border-gray-800 flex flex-col h-full;
  min-height: 280px;
}

/* Стили для вертикальных планшетов - по две карточки в строку */
@media (min-width: 640px) and (max-width: 1023px) {
  #for-whom .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.business-icon {
  @apply text-primary dark:text-primary-dark text-3xl mb-4 bg-blue-50 dark:bg-blue-900/20 w-16 h-16 rounded-full flex items-center justify-center;
}

.business-card h3 {
  @apply text-xl font-semibold text-secondary dark:text-light mb-4;
}

.business-features {
  @apply space-y-2 mt-2 text-gray-600 dark:text-gray-300 text-sm;
}

.business-features li {
  @apply flex items-start;
}

.business-features li:before {
  content: '\2022'; /* Маркер списка */
  @apply text-primary dark:text-primary-dark mr-2 font-bold;
}

/**
 * Стили для секций и заголовков
 * Общие стили для секций и их заголовков
 */
.section {
  @apply py-16 md:py-24;
}

.section-title {
  @apply text-center mb-12 md:mb-16;
}

.section-title h2 {
  @apply text-2xl md:text-3xl lg:text-4xl font-bold text-secondary dark:text-light mb-4 tracking-tight relative inline-block;
}

.section-title h2::after {
  content: '';
  @apply absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-primary dark:bg-primary-dark mt-2;
  bottom: -10px;
}

.section-title p {
  @apply text-base md:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto;
}

/**
 * Стили для страницы документации
 * Специальные стили для страницы документации и загрузки файлов
 */

/* Стили для карточек загрузки */
.download-card {
  @apply bg-white dark:bg-dark-dark rounded-xl p-8 shadow-md transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-xl dark:shadow-black/30 border border-gray-100 dark:border-gray-800 flex flex-col justify-between h-full relative overflow-hidden;
  background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  min-height: 280px;
}

/* Стили для карточек загрузки на планшетах */
@media (min-width: 640px) and (max-width: 767px) {
  .download-card {
    @apply p-6;
    min-height: 260px;
  }

  .download-card h3 {
    @apply text-lg;
  }

  .download-card p {
    @apply text-sm;
  }

  .download-icon {
    height: 60px;
  }

  .download-icon i {
    @apply text-5xl;
  }

  .download-card .btn {
    @apply py-2 px-4 text-sm;
  }

  .download-card .space-y-2 {
    @apply space-y-1;
  }
}

/* Стили для углового лейбла "Скоро" в виде SVG ленты */
.coming-soon-label {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background-image: url('assets/img/ribbon-corner-cut.svg');
  background-size: contain;
  background-repeat: no-repeat;
  z-index: 10;
}

.dark .coming-soon-label {
  background-image: url('assets/img/ribbon-corner-cut-dark.svg');
}

.download-icon {
  @apply text-5xl text-primary dark:text-primary-dark mb-6 flex items-center justify-center;
  height: 80px;
}

.download-icon i {
  @apply text-6xl;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.download-card:hover .download-icon i {
  transform: scale(1.1);
  filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.15));
}

.download-card h3 {
  @apply text-xl md:text-2xl font-semibold text-secondary dark:text-light mb-4 tracking-tight text-center;
}

.download-card .btn {
  transition: all 0.3s ease;
}

.download-card:hover .btn:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 102, 255, 0.25);
}

.btn.disabled {
  @apply opacity-60 cursor-not-allowed;
  pointer-events: none;
  box-shadow: none !important;
  transform: none !important;
}

/* Стили для кнопок */
.btn {
  @apply inline-flex items-center justify-center py-3 px-6 bg-primary dark:bg-primary-dark text-white font-medium rounded-lg transition-all duration-300 hover:bg-primary-dark dark:hover:bg-primary hover:shadow-lg dark:hover:shadow-blue-900/30 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark focus:ring-opacity-50;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-sm {
  @apply py-2 px-4 text-sm w-full justify-center;
}

.btn-outline {
  @apply bg-transparent border-2 border-primary dark:border-primary-dark text-primary dark:text-primary-dark hover:bg-primary hover:text-white dark:hover:bg-primary-dark dark:hover:text-white transition-all duration-300;
  position: relative;
  overflow: hidden;
  color: #0066ff; /* Явно задаем цвет текста для светлой темы */
}

.btn-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.btn-outline:hover::before {
  left: 100%;
}

/* Стили для секции документации */
.docs-content {
  @apply max-w-4xl mx-auto;
}

.docs-content h3 {
  @apply border-b border-gray-200 dark:border-gray-700 pb-4 mb-6;
}

.docs-content h4 {
  @apply border-l-4 border-primary dark:border-primary-dark pl-4;
}

/* Глобальные стили для тега code на всем сайте */
code {
  @apply bg-gray-200 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono text-primary dark:text-primary-dark;
}

/* Стили для FAQ */
.faq-content {
  @apply max-w-3xl mx-auto;
}



.faq-item {
  @apply bg-white dark:bg-dark-dark rounded-lg shadow-md overflow-hidden border border-gray-100 dark:border-gray-800 mb-4;
  background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
}

.faq-question {
  @apply p-4 cursor-pointer font-semibold text-lg text-secondary dark:text-light flex justify-between items-center transition-colors duration-300 hover:bg-gray-50 dark:hover:bg-gray-800;
}

.faq-question::after {
  content: '\002B';
  @apply text-xl font-bold text-primary dark:text-primary-dark transition-transform duration-300;
}

.faq-item.active .faq-question {
  @apply bg-gray-50 dark:bg-gray-800;
}

.faq-item.active .faq-question::after {
  content: '\2212';
  @apply transform rotate-180;
}

.faq-answer {
  @apply p-6 text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.faq-answer p {
  @apply mb-4;
}

.faq-answer ol, .faq-answer ul {
  @apply mb-4;
}

/**
 * Анимация для кнопок навигации в галерее
 * Эффект дрожания при попытке перейти за пределы галереи
 */
@keyframes button-shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-3px); }
  100% { transform: translateX(0); }
}

.button-shake {
  animation: button-shake 0.5s ease-in-out;
}
