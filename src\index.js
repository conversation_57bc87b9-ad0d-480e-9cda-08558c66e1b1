/**
 * @fileoverview Основной JavaScript файл для сайта Telegram Support Bot
 * Содержит инициализацию библиотек, обработчики событий и другие функции для интерактивности сайта
 * <AUTHOR> Agent
 * @version 1.2.6
 */

// Версия приложения
const APP_VERSION = '1.2.6';

// Дата сборки (автоматически обновляется при каждой сборке)
const BUILD_DATE = new Date().toISOString().slice(0, 10);

// Импортируем стили CSS для приложения
import './styles.css';

// Импортируем библиотеку AOS (Animate On Scroll) для анимаций при прокрутке
import AOS from 'aos';
// Импортируем стили библиотеки AOS
import 'aos/dist/aos.css';

/**
 * Регистрация Service Worker для управления кэшем и автоматического обновления сайта
 */
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/service-worker.js')
      .then(registration => {
        console.log('ServiceWorker registration successful with scope: ', registration.scope);

        // Проверка наличия обновлений Service Worker
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // Новый Service Worker установлен, но еще не активирован
              console.log('New content is available; please refresh.');

              // Показываем стилизованное модальное окно с уведомлением об обновлении
              window.showUpdateModal(newWorker);
            }
          });
        });
      })
      .catch(error => {
        console.error('ServiceWorker registration failed: ', error);
      });

    // Обработка сообщений от Service Worker
    let refreshing = false;
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('New controller detected');
      // Предотвращаем многократное обновление страницы
      if (refreshing) return;
      refreshing = true;

      // Вместо автоматического обновления показываем уведомление
      // Показываем модальное окно с уведомлением об обновлении
      const updateModal = document.getElementById('updateModal');
      if (updateModal) {
        updateModal.classList.remove('hidden');
        updateModal.classList.add('scale-100');
        updateModal.classList.remove('scale-95', 'opacity-0');
      }
    });
  });
}

/**
 * Инициализация всех скриптов после загрузки DOM
 * Этот обработчик события запускается после того, как структура HTML полностью загружена
 */
document.addEventListener('DOMContentLoaded', () => {
  // Обновляем версию приложения в футере
  const versionElement = document.getElementById('app-version');
  if (versionElement) {
    versionElement.textContent = `${APP_VERSION} (${BUILD_DATE})`;
  }

  /**
   * Инициализация FAQ на странице документации
   * Добавляет интерактивность для раздела часто задаваемых вопросов
   */
  const faqItems = document.querySelectorAll('.faq-item');

  if (faqItems.length > 0) {
    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      const answer = item.querySelector('.faq-answer');

      // Скрываем все ответы по умолчанию
      if (answer) {
        answer.style.display = 'none';

        // Добавляем обработчик клика на вопрос
        if (question) {
          question.addEventListener('click', () => {
            // Переключаем класс active
            item.classList.toggle('active');

            // Переключаем видимость ответа с анимацией
            if (answer.style.display === 'none') {
              answer.style.display = 'block';
              answer.style.maxHeight = '0';
              setTimeout(() => {
                answer.style.maxHeight = answer.scrollHeight + 'px';
              }, 10);
            } else {
              answer.style.maxHeight = '0';
              setTimeout(() => {
                answer.style.display = 'none';
              }, 300);
            }
          });
        }
      }
    });

    // Открываем первый вопрос по умолчанию
    if (faqItems[0]) {
      const firstQuestion = faqItems[0].querySelector('.faq-question');
      if (firstQuestion) {
        // Имитируем клик на первый вопрос
        setTimeout(() => {
          firstQuestion.click();
        }, 500);
      }
    }
  }

  // Добавляем обработчики событий для кнопок в модальном окне обновления
  const updateNowBtn = document.getElementById('updateNowBtn');
  const updateLaterBtn = document.getElementById('updateLaterBtn');
  const closeUpdateBtn = document.getElementById('closeUpdateBtn');

  if (updateNowBtn) {
    // Удаляем все существующие обработчики событий
    updateNowBtn.replaceWith(updateNowBtn.cloneNode(true));
    // Получаем новую ссылку на элемент после клонирования
    const newUpdateNowBtn = document.getElementById('updateNowBtn');
    // Добавляем новый обработчик события
    newUpdateNowBtn.addEventListener('click', function() {
      window.updateNow();
    });
  }

  if (updateLaterBtn) {
    // Удаляем все существующие обработчики событий
    updateLaterBtn.replaceWith(updateLaterBtn.cloneNode(true));
    // Получаем новую ссылку на элемент после клонирования
    const newUpdateLaterBtn = document.getElementById('updateLaterBtn');
    // Добавляем новый обработчик события
    newUpdateLaterBtn.addEventListener('click', function() {
      window.closeUpdateModal();
    });
  }

  if (closeUpdateBtn) {
    // Удаляем все существующие обработчики событий
    closeUpdateBtn.replaceWith(closeUpdateBtn.cloneNode(true));
    // Получаем новую ссылку на элемент после клонирования
    const newCloseUpdateBtn = document.getElementById('closeUpdateBtn');
    // Добавляем новый обработчик события
    newCloseUpdateBtn.addEventListener('click', function() {
      window.closeUpdateModal();
    });
  }
  // Добавляем класс loaded после загрузки страницы для активации плавных переходов
  document.documentElement.classList.add('loaded');

  /**
   * Инициализация библиотеки AOS с оптимизированными настройками
   * AOS отвечает за анимации элементов при прокрутке страницы
   */
  AOS.init({
    duration: 800,         // Продолжительность анимации в миллисекундах
    easing: 'ease-in-out', // Тип плавности анимации
    once: true,            // Анимация происходит только один раз (не повторяется при прокрутке вверх)
    offset: 50,            // Меньший оффсет для более плавной анимации
    anchorPlacement: 'top-center', // Измененное размещение якоря для триггера анимации
    disableMutationObserver: false, // Разрешаем отслеживание изменений DOM
    startEvent: 'load',    // Запуск анимаций только после полной загрузки страницы
  });

  /**
   * Улучшенная обработка мобильного меню
   * Реализует открытие/закрытие меню на мобильных устройствах с анимацией
   */
  const mobileMenuButton = document.querySelector('.mobile-menu-button'); // Кнопка мобильного меню
  const navLinks = document.querySelector('.nav-links');             // Блок с навигационными ссылками
  const body = document.body;                                       // Элемент body для добавления класса при открытом меню

  // Проверяем, что кнопка мобильного меню существует на странице
  if (mobileMenuButton && navLinks) {
    // Добавляем обработчик клика на кнопку меню
    mobileMenuButton.addEventListener('click', () => {
      // Переключаем классы для показа/скрытия меню
      navLinks.classList.toggle('hidden'); // Скрытие/показ меню
      navLinks.classList.toggle('flex');   // Изменение стиля отображения

      // Добавляем класс для анимации иконки меню
      body.classList.toggle('nav-open');

      // Добавляем атрибут aria-expanded для доступности
      const isExpanded = navLinks.classList.contains('flex');
      mobileMenuButton.setAttribute('aria-expanded', isExpanded);
    });
  }

  /**
   * Закрытие мобильного меню при клике на пункт меню
   * Улучшает пользовательский опыт, автоматически скрывая меню после выбора пункта
   */
  if (navLinks) {
    // Находим все ссылки в меню и добавляем обработчики клика
    navLinks.querySelectorAll('a').forEach(link => {
      link.addEventListener('click', () => {
        // Проверяем, что мы на мобильном устройстве (ширина экрана < 768px)
        if (window.innerWidth < 768) {
          // Скрываем меню после клика на пункт
          navLinks.classList.add('hidden');
          navLinks.classList.remove('flex');

          // Убираем класс для анимации иконки меню
          body.classList.remove('nav-open');

          // Обновляем атрибут aria-expanded
          if (mobileMenuButton) {
            mobileMenuButton.setAttribute('aria-expanded', 'false');
          }
        }
      });
    });
  }

  // Закрытие меню при клике вне меню
  document.addEventListener('click', (e) => {
    if (navLinks && !navLinks.classList.contains('hidden') &&
        !navLinks.contains(e.target) &&
        !mobileMenuButton.contains(e.target)) {
      navLinks.classList.add('hidden');
      navLinks.classList.remove('flex');
      body.classList.remove('nav-open');
      if (mobileMenuButton) {
        mobileMenuButton.setAttribute('aria-expanded', 'false');
      }
    }
  });

  /**
   * Обработчик клика на логотип
   * Предотвращает стандартное поведение ссылки на логотипе, чтобы избежать перезагрузки страницы
   */
  const logoLink = document.querySelector('.logo-link');
  if (logoLink) {
    logoLink.addEventListener('click', function(e) {
      e.preventDefault(); // Отменяем стандартное поведение ссылки
      // Ничего не делаем при клике на логотип, чтобы избежать мигания или перезагрузки
    });
  }

  /**
   * Реализация плавной прокрутки для якорных ссылок
   * Позволяет плавно перемещаться к различным секциям страницы при клике на меню
   */
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    // Получаем значение атрибута href
    const href = anchor.getAttribute('href');

    // Проверяем, что это не пустой якорь ('#')
    if (href && href !== '#') {
      // Добавляем обработчик клика для плавной прокрутки
      anchor.addEventListener('click', function (e) {
        e.preventDefault(); // Отменяем стандартное поведение ссылки

        // Находим целевой элемент по идентификатору
        const targetElement = document.querySelector(href);
        if (targetElement) {
          // Плавно прокручиваем страницу к этому элементу
          targetElement.scrollIntoView({
            behavior: 'smooth' // Плавная анимация прокрутки
          });
        }
      });
    }
  });

  /**
   * Функции для модальной галереи изображений
   * Позволяет показывать изображения в полноэкранном режиме при клике на них
   * с возможностью навигации между изображениями
   */

  // Массив для хранения всех изображений галереи
  let galleryImages = [];
  // Индекс текущего отображаемого изображения
  let currentImageIndex = 0;

  /**
   * Инициализация массива изображений галереи
   * Собирает все изображения из секции скриншотов в визуальном порядке
   */
  function initGalleryImages() {
    galleryImages = [];
    // Находим все элементы скриншотов
    const screenshotItems = document.querySelectorAll('.screenshot-item');

    // Создаем массив объектов с информацией о позиции и данными изображения
    const itemsWithPosition = [];

    // Проходим по каждому элементу и извлекаем информацию об изображении
    screenshotItems.forEach((item, index) => {
      // Проверяем, что у элемента есть атрибут onclick
      const onclickAttr = item.getAttribute('onclick');
      if (onclickAttr) {
        // Извлекаем путь к изображению и подпись из атрибута onclick
        const match = onclickAttr.match(/openModal\('([^']+)',\s*'([^']*)'\)/);
        if (match && match.length >= 3) {
          const imgSrc = match[1];
          const caption = match[2];

          // Получаем позицию элемента на странице
          const rect = item.getBoundingClientRect();

          // Добавляем информацию об изображении и его позиции в массив
          itemsWithPosition.push({
            src: imgSrc,
            caption: caption,
            index: index,
            // Используем индекс в DOM для сортировки
            domIndex: Array.from(screenshotItems).indexOf(item),
            // Используем позицию в сетке для сортировки
            row: Math.floor(rect.top / 100),
            col: Math.floor(rect.left / 100)
          });
        }
      }
    });

    // Сортируем элементы по их позиции в DOM
    // Это гарантирует, что порядок будет соответствовать визуальному расположению на странице
    itemsWithPosition.sort((a, b) => a.domIndex - b.domIndex);

    // Заполняем массив изображений в отсортированном порядке
    galleryImages = itemsWithPosition.map(item => ({ src: item.src, caption: item.caption }));

    // Для отладки
    console.log('Gallery images initialized:', galleryImages.length);
  }

  // Инициализируем массив изображений при загрузке страницы
  initGalleryImages();

  /**
   * Создает индикатор положения в галерее
   * Добавляет точки-индикаторы для каждого изображения в галерее
   */
  function createGalleryIndicator() {
    const indicator = document.getElementById('galleryIndicator');
    if (!indicator) return;

    // Очищаем существующие индикаторы
    indicator.innerHTML = '';

    // Создаем точку для каждого изображения
    galleryImages.forEach((_, index) => {
      const dot = document.createElement('div');
      dot.className = 'gallery-dot' + (index === currentImageIndex ? ' active' : '');
      dot.addEventListener('click', () => {
        // Переходим к соответствующему изображению при клике на точку
        navigateToImage(index);
      });
      indicator.appendChild(dot);
    });
  }

  /**
   * Обновляет индикатор положения в галерее
   * Подсвечивает текущую точку и снимает выделение с остальных
   */
  function updateGalleryIndicator() {
    const dots = document.querySelectorAll('.gallery-dot');
    dots.forEach((dot, index) => {
      if (index === currentImageIndex) {
        dot.classList.add('active');
      } else {
        dot.classList.remove('active');
      }
    });
  }

  /**
   * Предзагрузка изображений
   * Загружает соседние изображения для ускорения навигации
   */
  function preloadAdjacentImages() {
    // Предзагрузка следующего изображения
    if (currentImageIndex < galleryImages.length - 1) {
      const nextImage = new Image();
      nextImage.src = galleryImages[currentImageIndex + 1].src;
    }

    // Предзагрузка предыдущего изображения
    if (currentImageIndex > 0) {
      const prevImage = new Image();
      prevImage.src = galleryImages[currentImageIndex - 1].src;
    }
  }

  /**
   * Открывает модальное окно с увеличенным изображением
   * @param {string} imgSrc - Путь к изображению
   * @param {string} captionText - Текст подписи к изображению
   */
  window.openModal = function(imgSrc, captionText) {
    // Получаем элементы модального окна
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    const caption = document.getElementById('modalCaption');
    const imageContainer = document.getElementById('imageContainer');

    // Обновляем массив изображений на случай, если DOM изменился
    initGalleryImages();

    // Находим индекс текущего изображения в массиве
    currentImageIndex = galleryImages.findIndex(img => img.src === imgSrc);

    // Показываем модальное окно
    modal.classList.remove('hidden');

    // Сбрасываем зум, если он был активен
    if (imageContainer.classList.contains('zoomed')) {
      imageContainer.classList.remove('zoomed');
      modalImg.style.transform = '';
    }

    // Устанавливаем изображение и подпись
    modalImg.classList.add('fade-out');

    // Добавляем небольшую задержку для анимации
    setTimeout(() => {
      modalImg.src = imgSrc;
      caption.textContent = captionText;

      // Создаем индикатор положения в галерее
      createGalleryIndicator();

      // Предзагружаем соседние изображения
      preloadAdjacentImages();

      // Плавно показываем изображение
      modalImg.classList.remove('fade-out');
      modalImg.classList.add('fade-in');
    }, 300);

    // Предотвращаем прокрутку страницы под модальным окном
    document.body.classList.add('overflow-hidden');

    // Обновляем видимость кнопок навигации
    updateNavigationButtons();
  };

  /**
   * Закрывает модальное окно с изображением
   */
  window.closeModal = function() {
    console.log('Закрытие модального окна галереи');
    const modal = document.getElementById('imageModal');
    // Скрываем модальное окно
    modal.classList.add('hidden');

    // Восстанавливаем возможность прокрутки страницы
    document.body.classList.remove('overflow-hidden');
  };

  /**
   * Обновляет видимость кнопок навигации в зависимости от текущего индекса
   */
  function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevImageBtn');
    const nextBtn = document.getElementById('nextImageBtn');

    // Скрываем кнопку "Предыдущее", если мы на первом изображении
    if (prevBtn) {
      prevBtn.style.visibility = currentImageIndex <= 0 ? 'hidden' : 'visible';
    }

    // Скрываем кнопку "Следующее", если мы на последнем изображении
    if (nextBtn) {
      nextBtn.style.visibility = currentImageIndex >= galleryImages.length - 1 ? 'hidden' : 'visible';
    }
  }

  /**
   * Переход к конкретному изображению по индексу
   * @param {number} index - Индекс изображения в массиве galleryImages
   */
  function navigateToImage(index) {
    if (index >= 0 && index < galleryImages.length && index !== currentImageIndex) {
      // Получаем элементы
      const modalImg = document.getElementById('modalImage');
      const caption = document.getElementById('modalCaption');
      const imageContainer = document.getElementById('imageContainer');

      // Сбрасываем зум, если он был активен
      if (imageContainer.classList.contains('zoomed')) {
        imageContainer.classList.remove('zoomed');
        modalImg.style.transform = '';
      }

      // Добавляем анимацию затухания
      modalImg.classList.add('fade-out');

      // Обновляем текущий индекс
      currentImageIndex = index;

      // Добавляем небольшую задержку для анимации
      setTimeout(() => {
        // Получаем информацию о новом изображении
        const newImage = galleryImages[currentImageIndex];

        // Обновляем изображение и подпись
        modalImg.src = newImage.src;
        caption.textContent = newImage.caption;

        // Плавно показываем изображение
        modalImg.classList.remove('fade-out');
        modalImg.classList.add('fade-in');

        // Обновляем индикатор галереи
        updateGalleryIndicator();

        // Предзагружаем соседние изображения
        preloadAdjacentImages();
      }, 300);

      // Обновляем видимость кнопок навигации
      updateNavigationButtons();
    }
  }

  /**
   * Навигация по галерее изображений
   * @param {number} direction - Направление навигации (-1 для предыдущего, 1 для следующего)
   */
  window.navigateGallery = function(direction) {
    // Проверяем, что массив изображений не пуст
    if (galleryImages.length === 0) {
      console.warn('Массив изображений пуст');
      return;
    }

    // Вычисляем новый индекс
    const newIndex = currentImageIndex + direction;

    // Проверяем, что новый индекс находится в пределах массива
    if (newIndex >= 0 && newIndex < galleryImages.length) {
      // Используем функцию навигации к конкретному изображению
      navigateToImage(newIndex);
    } else {
      // Если мы достигли конца галереи, можно добавить визуальный эффект
      // Например, небольшое дрожание или подсветку кнопки
      const button = direction > 0 ? document.getElementById('nextImageBtn') : document.getElementById('prevImageBtn');
      if (button) {
        // Добавляем класс для анимации и удаляем его через небольшое время
        button.classList.add('button-shake');
        setTimeout(() => {
          button.classList.remove('button-shake');
        }, 500);
      }
    }
  };

  /**
   * Закрытие модального окна при клике вне изображения и элементов управления
   * Позволяет закрыть модальное окно кликом по затемненной области
   */
  const modal = document.getElementById('imageModal');
  if (modal) {
    modal.addEventListener('click', function(event) {
      // Проверяем, что клик был не по изображению, кнопкам навигации или подписи
      const modalContent = document.querySelector('#imageModal > div');
      const closeBtn = document.querySelector('#imageModal > button');

      if (event.target === this &&
          !modalContent.contains(event.target) &&
          event.target !== closeBtn) {
        window.closeModal();
      }
    });
  }

  // Коэффициент увеличения для зума
  const ZOOM_SCALE = 2.5;

  /**
   * Функция для управления зумом изображения
   * @param {Event} event - Событие клика или касания
   */
  window.toggleImageZoom = function(event) {
    // Предотвращаем всплытие события
    if (event.stopPropagation) {
      event.stopPropagation();
    }

    const imageContainer = document.getElementById('imageContainer');
    const modalImg = document.getElementById('modalImage');

    // Переключаем класс зума
    if (imageContainer.classList.contains('zoomed')) {
      // Убираем зум с плавной анимацией
      modalImg.style.transition = 'transform 0.3s ease-out';
      modalImg.style.transform = 'scale(1) translate(0, 0)';

      // Сбрасываем переменные зума
      currentScale = 1;
      transformX = 0;
      transformY = 0;

      // Убираем класс зума после завершения анимации
      setTimeout(() => {
        imageContainer.classList.remove('zoomed');
        // Меняем курсор на zoom-in
        imageContainer.style.cursor = 'zoom-in';
      }, 300);
    } else {
      // Добавляем зум и центрируем на месте клика
      imageContainer.classList.add('zoomed');
      // Меняем курсор на zoom-out
      imageContainer.style.cursor = 'zoom-out';

      // Вычисляем позицию клика относительно изображения
      const rect = modalImg.getBoundingClientRect();
      let x = 0.5; // По умолчанию центр
      let y = 0.5; // По умолчанию центр

      // Определяем, является ли событие касанием или кликом
      if (event.touches && event.touches.length > 0) {
        // Событие касания
        x = (event.touches[0].clientX - rect.left) / rect.width;
        y = (event.touches[0].clientY - rect.top) / rect.height;
      } else if (event.clientX && event.clientY) {
        // Событие клика мышью
        x = (event.clientX - rect.left) / rect.width;
        y = (event.clientY - rect.top) / rect.height;
      }

      // Устанавливаем масштаб и позицию с плавной анимацией
      modalImg.style.transition = 'transform 0.3s ease-out';
      modalImg.style.transformOrigin = `${x * 100}% ${y * 100}%`;
      modalImg.style.transform = `scale(${ZOOM_SCALE})`;

      // Устанавливаем текущий масштаб
      currentScale = ZOOM_SCALE;
    }
  };

  /**
   * Функция для перемещения увеличенного изображения
   * @param {Event} event - Событие движения мыши или касания
   */
  window.moveZoomedImage = function(event) {
    const imageContainer = document.getElementById('imageContainer');
    const modalImg = document.getElementById('modalImage');

    // Проверяем, что изображение увеличено
    if (imageContainer.classList.contains('zoomed')) {
      // Отключаем анимацию для плавного перемещения
      modalImg.style.transition = 'none';

      // Вычисляем позицию курсора или касания относительно изображения
      const rect = modalImg.getBoundingClientRect();
      let x = 0.5; // По умолчанию центр
      let y = 0.5; // По умолчанию центр

      // Определяем, является ли событие касанием или движением мыши
      if (event.touches && event.touches.length > 0) {
        // Событие касания
        x = (event.touches[0].clientX - rect.left) / rect.width;
        y = (event.touches[0].clientY - rect.top) / rect.height;
      } else if (event.clientX && event.clientY) {
        // Событие движения мыши
        x = (event.clientX - rect.left) / rect.width;
        y = (event.clientY - rect.top) / rect.height;
      }

      // Обновляем точку трансформации
      modalImg.style.transformOrigin = `${x * 100}% ${y * 100}%`;
    }
  };

  // Переменные для отслеживания свайпов и жестов pinch-to-zoom
  let touchStartX = 0;
  let touchEndX = 0;
  let initialPinchDistance = 0;
  let currentScale = 1;
  let initialScale = 1;
  let lastTouchX = 0;
  let lastTouchY = 0;
  let startTouchX = 0;
  let startTouchY = 0;
  let isPinching = false;
  let isMoving = false;
  let transformX = 0;
  let transformY = 0;

  /**
   * Вычисляет расстояние между двумя точками касания
   * @param {TouchEvent} event - Событие касания
   * @returns {number} Расстояние между точками
   */
  function getPinchDistance(event) {
    if (event.touches.length < 2) return 0;

    const dx = event.touches[0].clientX - event.touches[1].clientX;
    const dy = event.touches[0].clientY - event.touches[1].clientY;

    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * Получает центр между двумя точками касания
   * @param {TouchEvent} event - Событие касания
   * @returns {Object} Координаты центра {x, y}
   */
  function getPinchCenter(event) {
    if (event.touches.length < 2) {
      return { x: event.touches[0].clientX, y: event.touches[0].clientY };
    }

    return {
      x: (event.touches[0].clientX + event.touches[1].clientX) / 2,
      y: (event.touches[0].clientY + event.touches[1].clientY) / 2
    };
  }

  /**
   * Обработчик начала касания для свайпа и жеста pinch-to-zoom
   */
  function handleTouchStart(event) {
    // Обработка свайпа
    touchStartX = event.changedTouches[0].screenX;

    // Обработка жеста pinch-to-zoom
    const imageContainer = document.getElementById('imageContainer');
    const modalImg = document.getElementById('modalImage');

    if (event.touches.length === 2) {
      // Начало жеста pinch-to-zoom
      event.preventDefault();
      isPinching = true;
      isMoving = false;

      // Запоминаем начальное расстояние между пальцами
      initialPinchDistance = getPinchDistance(event);

      // Запоминаем текущий масштаб
      const transform = modalImg.style.transform;
      const scaleMatch = transform.match(/scale\(([^)]+)\)/);
      initialScale = scaleMatch ? parseFloat(scaleMatch[1]) : 1;
      currentScale = initialScale;

      // Добавляем класс зума, если его еще нет
      if (!imageContainer.classList.contains('zoomed')) {
        imageContainer.classList.add('zoomed');
      }
    } else if (event.touches.length === 1) {
      // Начало перемещения изображения
      if (imageContainer.classList.contains('zoomed')) {
        isMoving = true;
        isPinching = false;

        // Запоминаем начальную позицию касания
        startTouchX = event.touches[0].clientX;
        startTouchY = event.touches[0].clientY;
        lastTouchX = startTouchX;
        lastTouchY = startTouchY;

        // Получаем текущие значения трансформации
        const transform = modalImg.style.transform;
        const translateMatch = transform.match(/translate\(([^,]+)px,\s*([^)]+)px\)/);
        if (translateMatch) {
          transformX = parseFloat(translateMatch[1]);
          transformY = parseFloat(translateMatch[2]);
        } else {
          transformX = 0;
          transformY = 0;
        }
      }
    }
  }

  /**
   * Обработчик движения пальцев для жеста pinch-to-zoom
   */
  function handleTouchMove(event) {
    const imageContainer = document.getElementById('imageContainer');
    const modalImg = document.getElementById('modalImage');

    if (!imageContainer || !modalImg) return;

    if (isPinching && event.touches.length === 2) {
      // Предотвращаем скроллинг страницы
      event.preventDefault();

      // Вычисляем новое расстояние между пальцами
      const currentDistance = getPinchDistance(event);

      // Вычисляем новый масштаб
      if (initialPinchDistance > 0) {
        currentScale = initialScale * (currentDistance / initialPinchDistance);

        // Ограничиваем масштаб между 1 и 5
        currentScale = Math.max(1, Math.min(5, currentScale));

        // Получаем центр между пальцами
        const center = getPinchCenter(event);
        const rect = modalImg.getBoundingClientRect();

        // Вычисляем относительные координаты центра относительно изображения
        const x = (center.x - rect.left) / rect.width;
        const y = (center.y - rect.top) / rect.height;

        // Устанавливаем точку трансформации и масштаб
        modalImg.style.transformOrigin = `${x * 100}% ${y * 100}%`;
        modalImg.style.transform = `scale(${currentScale})`;
      }
    } else if (isMoving && event.touches.length === 1 && imageContainer.classList.contains('zoomed')) {
      // Перемещение увеличенного изображения
      event.preventDefault();

      const currentX = event.touches[0].clientX;
      const currentY = event.touches[0].clientY;

      // Вычисляем разницу между текущей и предыдущей позицией
      const deltaX = currentX - lastTouchX;
      const deltaY = currentY - lastTouchY;

      // Обновляем позицию трансформации
      transformX += deltaX;
      transformY += deltaY;

      // Ограничиваем перемещение в пределах разумного
      const rect = modalImg.getBoundingClientRect();
      const maxTransformX = rect.width * (currentScale - 1) / 2;
      const maxTransformY = rect.height * (currentScale - 1) / 2;

      transformX = Math.max(-maxTransformX, Math.min(maxTransformX, transformX));
      transformY = Math.max(-maxTransformY, Math.min(maxTransformY, transformY));

      // Применяем трансформацию
      modalImg.style.transform = `scale(${currentScale}) translate(${transformX}px, ${transformY}px)`;

      // Обновляем последнюю позицию
      lastTouchX = currentX;
      lastTouchY = currentY;
    }
  }

  /**
   * Обработчик окончания касания для свайпа и жеста pinch-to-zoom
   */
  function handleTouchEnd(event) {
    // Обработка свайпа
    touchEndX = event.changedTouches[0].screenX;

    const imageContainer = document.getElementById('imageContainer');

    // Если изображение не увеличено, обрабатываем свайп
    if (!imageContainer.classList.contains('zoomed')) {
      handleSwipe();
    }

    // Сбрасываем флаги для жеста pinch-to-zoom
    isPinching = false;
    isMoving = false;

    // Если масштаб близок к 1, сбрасываем зум
    if (currentScale < 1.1) {
      const modalImg = document.getElementById('modalImage');
      modalImg.style.transition = 'transform 0.3s ease-out';
      modalImg.style.transform = 'scale(1) translate(0, 0)';

      setTimeout(() => {
        imageContainer.classList.remove('zoomed');
        imageContainer.style.cursor = 'zoom-in';
        currentScale = 1;
        transformX = 0;
        transformY = 0;
      }, 300);
    }
  }

  /**
   * Обработка свайпа
   */
  function handleSwipe() {
    // Проверяем, что модальное окно открыто
    const modal = document.getElementById('imageModal');
    const imageContainer = document.getElementById('imageContainer');

    if (modal && !modal.classList.contains('hidden')) {
      // Проверяем, что изображение не в режиме зума
      if (!imageContainer.classList.contains('zoomed')) {
        const swipeThreshold = 50; // Минимальное расстояние для свайпа

        if (touchEndX - touchStartX > swipeThreshold) {
          // Свайп вправо - предыдущее изображение
          window.navigateGallery(-1);
        } else if (touchStartX - touchEndX > swipeThreshold) {
          // Свайп влево - следующее изображение
          window.navigateGallery(1);
        }
      }
    }
  }

  // Добавляем обработчики свайпа и жеста pinch-to-zoom к модальному окну
  const modalElement = document.getElementById('imageModal');
  if (modalElement) {
    modalElement.addEventListener('touchstart', handleTouchStart, { passive: false });
    modalElement.addEventListener('touchmove', handleTouchMove, { passive: false });
    modalElement.addEventListener('touchend', handleTouchEnd, { passive: false });
  }

  // Добавляем обработчики событий для изображения
  const modalImage = document.getElementById('modalImage');
  if (modalImage) {
    // Предотвращаем стандартный зум браузера на мобильных устройствах
    modalImage.addEventListener('touchstart', function(e) {
      if (e.touches.length >= 2) {
        e.preventDefault();
      }
    }, { passive: false });
  }

  // Добавляем улучшенный обработчик события для кнопки закрытия галереи
  const closeGalleryBtn = document.getElementById('closeGalleryBtn');
  if (closeGalleryBtn) {
    // Удаляем все существующие обработчики событий
    closeGalleryBtn.replaceWith(closeGalleryBtn.cloneNode(true));
    // Получаем новую ссылку на элемент после клонирования
    const newCloseGalleryBtn = document.getElementById('closeGalleryBtn');

    // Удаляем атрибут onclick для предотвращения конфликтов
    newCloseGalleryBtn.removeAttribute('onclick');

    // Добавляем несколько обработчиков событий для лучшей поддержки мобильных устройств
    const closeModalHandler = function(e) {
      e.preventDefault();
      e.stopPropagation();
      window.closeModal();
    };

    // Добавляем обработчики для разных типов событий
    newCloseGalleryBtn.addEventListener('click', closeModalHandler);
    newCloseGalleryBtn.addEventListener('touchend', closeModalHandler, { passive: false });

    // Добавляем стиль для улучшения визуальной обратной связи при нажатии
    newCloseGalleryBtn.addEventListener('touchstart', function(e) {
      e.preventDefault();
      this.style.opacity = '0.7';
    }, { passive: false });

    newCloseGalleryBtn.addEventListener('touchmove', function(e) {
      e.preventDefault();
    }, { passive: false });

    newCloseGalleryBtn.addEventListener('touchcancel', function() {
      this.style.opacity = '1';
    }, { passive: false });
  }

  // Добавляем обработчик события для нижней кнопки закрытия галереи
  const closeGalleryBtnBottom = document.getElementById('closeGalleryBtnBottom');
  if (closeGalleryBtnBottom) {
    closeGalleryBtnBottom.addEventListener('click', function() {
      window.closeModal();
    });

    // Добавляем обработчик для сенсорных экранов
    closeGalleryBtnBottom.addEventListener('touchend', function(e) {
      e.preventDefault();
      window.closeModal();
    }, { passive: false });
  }

  // Добавляем обработчики событий для кнопок навигации в галерее
  const prevImageBtn = document.getElementById('prevImageBtn');
  const nextImageBtn = document.getElementById('nextImageBtn');

  if (prevImageBtn) {
    // Удаляем все существующие обработчики событий
    prevImageBtn.replaceWith(prevImageBtn.cloneNode(true));
    // Получаем новую ссылку на элемент после клонирования
    const newPrevImageBtn = document.getElementById('prevImageBtn');
    // Добавляем новый обработчик события
    newPrevImageBtn.addEventListener('click', function(e) {
      e.preventDefault(); // Предотвращаем стандартное поведение
      window.navigateGallery(-1);
    });

    // Добавляем обработчик события для иконки внутри кнопки
    const prevIcon = newPrevImageBtn.querySelector('i');
    if (prevIcon) {
      prevIcon.addEventListener('click', function(e) {
        e.preventDefault(); // Предотвращаем стандартное поведение
        e.stopPropagation(); // Останавливаем всплытие события
        window.navigateGallery(-1);
      });
    }
  }

  if (nextImageBtn) {
    // Удаляем все существующие обработчики событий
    nextImageBtn.replaceWith(nextImageBtn.cloneNode(true));
    // Получаем новую ссылку на элемент после клонирования
    const newNextImageBtn = document.getElementById('nextImageBtn');
    // Добавляем новый обработчик события
    newNextImageBtn.addEventListener('click', function(e) {
      e.preventDefault(); // Предотвращаем стандартное поведение
      window.navigateGallery(1);
    });

    // Добавляем обработчик события для иконки внутри кнопки
    const nextIcon = newNextImageBtn.querySelector('i');
    if (nextIcon) {
      nextIcon.addEventListener('click', function(e) {
        e.preventDefault(); // Предотвращаем стандартное поведение
        e.stopPropagation(); // Останавливаем всплытие события
        window.navigateGallery(1);
      });
    }
  }

  /**
   * Обработка нажатий клавиш для навигации и закрытия
   * Улучшает пользовательский опыт, позволяя управлять галереей с клавиатуры
   */
  document.addEventListener('keydown', function(event) {
    // Проверяем, что модальное окно галереи открыто
    const imageModal = document.getElementById('imageModal');
    const imageContainer = document.getElementById('imageContainer');
    const demoModal = document.getElementById('demoModal');

    // Проверяем, открыто ли модальное окно демо-версии
    if (demoModal && !demoModal.classList.contains('hidden') && event.key === 'Escape') {
      window.closeDemoModal();
      return;
    }

    if (imageModal && !imageModal.classList.contains('hidden')) {
      // Проверяем, что изображение не в режиме зума для навигации
      const isZoomed = imageContainer.classList.contains('zoomed');

      switch(event.key) {
        case 'Escape':
          // Если изображение увеличено, сначала убираем зум
          if (isZoomed) {
            imageContainer.classList.remove('zoomed');
            document.getElementById('modalImage').style.transform = '';
          } else {
            // Иначе закрываем модальное окно
            window.closeModal();
          }
          break;
        case 'ArrowLeft':
          // Переходим к предыдущему изображению только если не в режиме зума
          if (!isZoomed) window.navigateGallery(-1);
          break;
        case 'ArrowRight':
          // Переходим к следующему изображению только если не в режиме зума
          if (!isZoomed) window.navigateGallery(1);
          break;
        case '+': // Увеличить изображение
        case '=':
          if (!isZoomed) {
            // Симулируем клик по центру изображения
            const modalImg = document.getElementById('modalImage');
            const rect = modalImg.getBoundingClientRect();
            const fakeEvent = {
              clientX: rect.left + rect.width / 2,
              clientY: rect.top + rect.height / 2,
              stopPropagation: function() {} // Добавляем пустую функцию для совместимости
            };
            window.toggleImageZoom(fakeEvent);
          }
          break;
        case '-': // Уменьшить изображение
          if (isZoomed) {
            const modalImg = document.getElementById('modalImage');
            // Плавно убираем зум
            modalImg.style.transition = 'transform 0.3s ease-out';
            modalImg.style.transform = 'scale(1) translate(0, 0)';

            // Убираем класс зума после завершения анимации
            setTimeout(() => {
              imageContainer.classList.remove('zoomed');
              imageContainer.style.cursor = 'zoom-in';
            }, 300);
          }
          break;
      }
    }
  });

  /**
   * Функции для управления модальными окнами
   * Полностью переработанный подход к обработке модальных окон
   */

  // Создаем объект для управления модальными окнами
  const modalManager = {
    // Функция для открытия модального окна
    openModal: function(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        // Закрываем все открытые модальные окна перед открытием нового
        document.querySelectorAll('.fixed.inset-0.z-50:not(.hidden)').forEach(openModal => {
          openModal.classList.add('hidden');
        });

        // Показываем модальное окно
        modal.classList.remove('hidden');

        // Предотвращаем прокрутку страницы под модальным окном
        document.body.classList.add('overflow-hidden');
      }
    },

    // Функция для закрытия модального окна
    closeModal: function(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        // Скрываем модальное окно
        modal.classList.add('hidden');

        // Восстанавливаем возможность прокрутки страницы
        document.body.classList.remove('overflow-hidden');
      }
    },

    // Инициализация обработчиков событий для модальных окон
    init: function() {
      // Инициализируем обработчики для кнопок закрытия модальных окон
      document.querySelectorAll('[onclick^="close"]').forEach(button => {
        const onclickAttr = button.getAttribute('onclick');
        if (onclickAttr) {
          // Удаляем атрибут onclick, чтобы избежать дублирования
          button.removeAttribute('onclick');

          // Определяем, какое модальное окно закрывает кнопка
          const modalId = onclickAttr.includes('closeDemoModal') ? 'demoModal' : 'pricingModal';

          // Добавляем новый обработчик
          button.addEventListener('click', () => this.closeModal(modalId));
        }
      });

      // Инициализируем обработчик для кнопки "Подключить полную версию" в модальном окне
      const connectFullVersionBtn = document.getElementById('connectFullVersionBtn');
      if (connectFullVersionBtn) {
        connectFullVersionBtn.addEventListener('click', () => {
          // Закрываем модальное окно перед переходом по ссылке
          this.closeModal('pricingModal');

          // Переходим по ссылке на Telegram бота с небольшой задержкой
          setTimeout(() => {
            window.open('https://t.me/rev_support_bot', '_blank');
          }, 300);
        });
      }

      // Инициализируем обработчики для кнопок в секции тарифов
      document.querySelectorAll('#pricing .pricing-card .btn.pricing-btn').forEach(button => {
        const buttonText = button.textContent.trim();

        // Обработчик для кнопки "Начать бесплатно"
        if (buttonText === 'Начать бесплатно') {
          button.addEventListener('click', (e) => {
            e.preventDefault();
            this.openModal('demoModal');
          });
        }
        // Обработчик для кнопки "Выбрать полную версию"
        else if (buttonText === 'Выбрать полную версию') {
          button.addEventListener('click', (e) => {
            e.preventDefault();
            this.openModal('pricingModal');
          });
        }
      });

      // Инициализируем обработчики для кнопок в CTA и Hero секциях
      document.querySelectorAll('.cta-section .btn, .hero-section .btn').forEach(button => {
        const buttonText = button.textContent.trim();
        if (buttonText === 'Начать бесплатно' || buttonText === 'Попробовать бесплатно') {
          button.addEventListener('click', (e) => {
            e.preventDefault();
            this.openModal('demoModal');
          });
        }
      });
    }
  };

  // Экспортируем функции в глобальный объект
  window.openDemoModal = function() { modalManager.openModal('demoModal'); };
  window.closeDemoModal = function() { modalManager.closeModal('demoModal'); };
  window.openPricingModal = function() { modalManager.openModal('pricingModal'); };
  window.closePricingModal = function() { modalManager.closeModal('pricingModal'); };

  // Инициализируем менеджер модальных окон
  modalManager.init();

  /**
   * Система согласия с политикой конфиденциальности
   * Показывает модальное окно при первом посещении сайта
   */
  const privacyConsentManager = {
    // Ключи для localStorage
    CONSENT_KEY: 'privacy_consent_given',
    CONSENT_DATA_KEY: 'privacy_consent_data',

    /**
     * Проверяет, было ли дано согласие ранее
     */
    hasConsent: function() {
      return localStorage.getItem(this.CONSENT_KEY) === 'true';
    },

    /**
     * Получает IP адрес пользователя
     */
    getUserIP: async function() {
      try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        return data.ip;
      } catch (error) {
        console.log('Не удалось получить IP адрес:', error);
        return 'unknown';
      }
    },

    /**
     * Получает источник перехода (referrer)
     */
    getSource: function() {
      return document.referrer || 'direct';
    },

    /**
     * Сохраняет данные о согласии
     */
    saveConsentData: async function(consent) {
      const ip = await this.getUserIP();
      const consentData = {
        consent: consent,
        ip: ip,
        date: new Date().toISOString(),
        source: this.getSource(),
        userAgent: navigator.userAgent,
        page: window.location.href
      };

      // Сохраняем в localStorage
      localStorage.setItem(this.CONSENT_KEY, consent.toString());
      localStorage.setItem(this.CONSENT_DATA_KEY, JSON.stringify(consentData));

      // Логируем данные локально
      this.logConsentLocally(consentData);

      return consentData;
    },

    /**
     * Сохраняет данные о согласии в localStorage для последующего экспорта
     */
    logConsentLocally: function(consentData) {
      try {
        // Получаем существующие логи
        const existingLogs = JSON.parse(localStorage.getItem('consent_logs') || '[]');

        // Добавляем новую запись
        const logEntry = {
          id: Date.now() + Math.random().toString(36).substr(2, 9),
          timestamp: new Date().toISOString(),
          ...consentData
        };

        existingLogs.push(logEntry);

        // Ограничиваем количество записей (последние 1000)
        if (existingLogs.length > 1000) {
          existingLogs.splice(0, existingLogs.length - 1000);
        }

        // Сохраняем обратно в localStorage
        localStorage.setItem('consent_logs', JSON.stringify(existingLogs));

        console.log('Согласие зафиксировано локально:', {
          consent: logEntry.consent,
          ip: logEntry.ip,
          date: logEntry.date,
          source: logEntry.source
        });

        // Также отправляем на внешний сервис аналитики (опционально)
        this.sendToAnalytics(consentData);

      } catch (error) {
        console.error('Ошибка локального логирования:', error);
      }
    },

    /**
     * Отправляет данные в Google Analytics или другую систему аналитики
     */
    sendToAnalytics: function(consentData) {
      // Google Analytics 4
      if (typeof gtag !== 'undefined') {
        gtag('event', 'privacy_consent', {
          'consent_given': consentData.consent,
          'source': consentData.source,
          'custom_parameter_ip': consentData.ip
        });
      }

      // Яндекс.Метрика
      if (typeof ym !== 'undefined') {
        ym(12345678, 'reachGoal', 'privacy_consent', {
          consent: consentData.consent,
          source: consentData.source
        });
      }

      // Отправка в webhook (если есть)
      if (window.CONSENT_WEBHOOK_URL) {
        fetch(window.CONSENT_WEBHOOK_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(consentData)
        }).catch(error => console.log('Webhook недоступен:', error));
      }
    },

    /**
     * Показывает модальное окно согласия
     */
    showConsentModal: function() {
      const modal = document.getElementById('privacyConsentModal');
      if (modal) {
        modal.classList.remove('hidden');
        // Предотвращаем прокрутку страницы
        document.body.classList.add('overflow-hidden');
      }
    },

    /**
     * Скрывает модальное окно согласия
     */
    hideConsentModal: function() {
      const modal = document.getElementById('privacyConsentModal');
      if (modal) {
        modal.classList.add('hidden');
        // Восстанавливаем прокрутку страницы
        document.body.classList.remove('overflow-hidden');
      }
    },

    /**
     * Инициализация системы согласия
     */
    init: function() {
      // Проверяем, нужно ли показывать модальное окно
      if (!this.hasConsent()) {
        // Определяем логику показа в зависимости от страницы
        if (window.location.pathname.includes('privacy.html')) {
          // На странице политики конфиденциальности показываем после прокрутки до конца
          this.initScrollToEndDetection();
        } else {
          // На остальных страницах показываем через 1 секунду
          setTimeout(() => {
            this.showConsentModal();
          }, 1000);
        }
      }

      // Инициализируем обработчики событий
      this.initEventHandlers();
    },

    /**
     * Инициализация отслеживания прокрутки до конца страницы
     */
    initScrollToEndDetection: function() {
      let hasShownModal = false;

      const checkScrollPosition = () => {
        if (hasShownModal) return;

        // Получаем высоту документа и позицию прокрутки
        const documentHeight = Math.max(
          document.body.scrollHeight,
          document.body.offsetHeight,
          document.documentElement.clientHeight,
          document.documentElement.scrollHeight,
          document.documentElement.offsetHeight
        );

        const windowHeight = window.innerHeight;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Проверяем, достиг ли пользователь конца страницы (с небольшим отступом)
        if (scrollTop + windowHeight >= documentHeight - 100) {
          hasShownModal = true;
          this.showConsentModal();
          // Удаляем обработчик после показа модального окна
          window.removeEventListener('scroll', checkScrollPosition);
        }
      };

      // Добавляем обработчик прокрутки
      window.addEventListener('scroll', checkScrollPosition);

      // Также проверяем при загрузке страницы (если страница короткая)
      setTimeout(() => {
        checkScrollPosition();
      }, 1000);
    },

    /**
     * Инициализация обработчиков событий
     */
    initEventHandlers: function() {
      const checkbox = document.getElementById('privacyConsentCheckbox');
      const acceptBtn = document.getElementById('privacyConsentAccept');
      const declineBtn = document.getElementById('privacyConsentDecline');

      // Обработчик изменения состояния чекбокса
      if (checkbox && acceptBtn) {
        checkbox.addEventListener('change', function() {
          acceptBtn.disabled = !this.checked;
        });
      }

      // Обработчик кнопки "Согласиться"
      if (acceptBtn) {
        acceptBtn.addEventListener('click', async () => {
          if (checkbox && checkbox.checked) {
            await this.saveConsentData(true);
            this.hideConsentModal();
            console.log('Согласие на обработку персональных данных получено');
          }
        });
      }

      // Обработчик кнопки "Отказаться"
      if (declineBtn) {
        declineBtn.addEventListener('click', async () => {
          await this.saveConsentData(false);
          this.hideConsentModal();
          console.log('Отказ от обработки персональных данных зафиксирован');
        });
      }
    }
  };

  // Инициализируем систему согласия с политикой конфиденциальности
  privacyConsentManager.init();

  /**
   * Административные функции для управления логами согласий
   * Доступны через консоль браузера для администраторов
   */
  window.ConsentAdmin = {
    /**
     * Экспортирует все логи согласий в JSON файл
     */
    exportLogs: function() {
      try {
        const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');
        const dataStr = JSON.stringify(logs, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `consent-logs-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        console.log(`✅ Экспортировано ${logs.length} записей в JSON`);
        return logs.length;
      } catch (error) {
        console.error('❌ Ошибка экспорта:', error);
        return 0;
      }
    },

    /**
     * Экспортирует логи в CSV формат для Excel
     */
    exportCSV: function() {
      try {
        const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');

        if (logs.length === 0) {
          alert('Нет данных для экспорта');
          return 0;
        }

        // Заголовки CSV с BOM для корректного отображения в Excel
        const BOM = '\uFEFF';
        const headers = ['ID', 'Дата', 'Время', 'Согласие', 'IP', 'Источник', 'Страница', 'Браузер'];

        // Формируем CSV строки
        const csvRows = [headers.join(';')]; // Используем ; для Excel

        logs.forEach(log => {
          const date = new Date(log.date);
          const row = [
            log.id,
            date.toLocaleDateString('ru-RU'),
            date.toLocaleTimeString('ru-RU'),
            log.consent ? 'Согласие' : 'Отказ',
            log.ip,
            `"${log.source.replace(/"/g, '""')}"`, // Экранируем кавычки
            `"${log.page.replace(/"/g, '""')}"`,
            `"${log.userAgent.substring(0, 100).replace(/"/g, '""')}"` // Обрезаем User Agent
          ];
          csvRows.push(row.join(';'));
        });

        const csvContent = BOM + csvRows.join('\n');
        const dataBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `consent-logs-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        console.log(`✅ Экспортировано ${logs.length} записей в CSV`);
        return logs.length;
      } catch (error) {
        console.error('❌ Ошибка экспорта CSV:', error);
        return 0;
      }
    },

    /**
     * Показывает статистику согласий
     */
    showStats: function() {
      try {
        const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

        const stats = {
          'Всего записей': logs.length,
          'Согласились': logs.filter(log => log.consent === true).length,
          'Отказались': logs.filter(log => log.consent === false).length,
          'Сегодня': logs.filter(log => new Date(log.date) >= today).length,
          'За неделю': logs.filter(log => new Date(log.date) >= weekAgo).length,
          'За месяц': logs.filter(log => new Date(log.date) >= monthAgo).length,
          'Уникальных IP': new Set(logs.map(log => log.ip)).size
        };

        console.table(stats);

        // Показываем также в alert для удобства
        const message = Object.entries(stats)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');

        alert(`📊 Статистика согласий:\n\n${message}`);

        return stats;
      } catch (error) {
        console.error('❌ Ошибка получения статистики:', error);
        return {};
      }
    },

    /**
     * Показывает последние записи в консоли
     */
    showRecentLogs: function(count = 10) {
      try {
        const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');
        const recentLogs = logs.slice(-count);

        console.log(`📋 Последние ${count} записей:`);
        console.table(recentLogs.map(log => ({
          'Дата': new Date(log.date).toLocaleString('ru-RU'),
          'Согласие': log.consent ? '✅ Да' : '❌ Нет',
          'IP': log.ip,
          'Источник': log.source.length > 30 ? log.source.substring(0, 30) + '...' : log.source
        })));

        return recentLogs;
      } catch (error) {
        console.error('❌ Ошибка получения логов:', error);
        return [];
      }
    },

    /**
     * Очищает все логи (только для администратора)
     */
    clearLogs: function() {
      if (confirm('⚠️ Вы уверены, что хотите удалить ВСЕ логи согласий?\n\nЭто действие необратимо!')) {
        localStorage.removeItem('consent_logs');
        console.log('🗑️ Все логи согласий удалены');
        alert('✅ Логи очищены');
        return true;
      }
      return false;
    },

    /**
     * Показывает справку по командам
     */
    help: function() {
      console.log(`
🔧 Административная панель согласий с политикой конфиденциальности

Доступные команды:
┌─────────────────────────────────────────────────────────────┐
│ ConsentAdmin.exportLogs()     - экспорт логов в JSON        │
│ ConsentAdmin.exportCSV()      - экспорт логов в CSV/Excel   │
│ ConsentAdmin.showStats()      - показать статистику         │
│ ConsentAdmin.showRecentLogs() - последние 10 записей        │
│ ConsentAdmin.clearLogs()      - очистить все логи           │
│ ConsentAdmin.help()           - эта справка                 │
└─────────────────────────────────────────────────────────────┘

Горячие клавиши:
• Ctrl+Shift+C - показать эту справку

Примеры использования:
• ConsentAdmin.showRecentLogs(20) - показать последние 20 записей
• ConsentAdmin.exportCSV() - скачать CSV файл для Excel
      `);
    }
  };

  // Добавляем горячие клавиши для администратора (Ctrl+Shift+C)
  document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.code === 'KeyC') {
      ConsentAdmin.help();
    }
  });

  // Показываем справку в консоли при загрузке (только в dev режиме)
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('🔧 Система согласий загружена. Нажмите Ctrl+Shift+C для справки администратора.');
  }

  /**
   * Административные функции для управления логами согласий
   */
  window.ConsentAdmin = {
    /**
     * Экспортирует все логи согласий в JSON файл
     */
    exportLogs: function() {
      try {
        const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');
        const dataStr = JSON.stringify(logs, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `consent-logs-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        console.log(`Экспортировано ${logs.length} записей`);
      } catch (error) {
        console.error('Ошибка экспорта:', error);
      }
    },

    /**
     * Экспортирует логи в CSV формат
     */
    exportCSV: function() {
      try {
        const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');

        if (logs.length === 0) {
          alert('Нет данных для экспорта');
          return;
        }

        // Заголовки CSV
        const headers = ['ID', 'Дата', 'Согласие', 'IP', 'Источник', 'Страница', 'User Agent'];

        // Формируем CSV строки
        const csvRows = [headers.join(',')];

        logs.forEach(log => {
          const row = [
            log.id,
            log.date,
            log.consent ? 'Да' : 'Нет',
            log.ip,
            `"${log.source}"`,
            `"${log.page}"`,
            `"${log.userAgent}"`
          ];
          csvRows.push(row.join(','));
        });

        const csvContent = csvRows.join('\n');
        const dataBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `consent-logs-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        console.log(`Экспортировано ${logs.length} записей в CSV`);
      } catch (error) {
        console.error('Ошибка экспорта CSV:', error);
      }
    },

    /**
     * Показывает статистику согласий
     */
    showStats: function() {
      try {
        const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');

        const stats = {
          total: logs.length,
          consented: logs.filter(log => log.consent === true).length,
          declined: logs.filter(log => log.consent === false).length,
          today: logs.filter(log => {
            const logDate = new Date(log.date);
            const today = new Date();
            return logDate.toDateString() === today.toDateString();
          }).length,
          thisWeek: logs.filter(log => {
            const logDate = new Date(log.date);
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return logDate >= weekAgo;
          }).length
        };

        console.table(stats);
        alert(`Статистика согласий:
Всего: ${stats.total}
Согласились: ${stats.consented}
Отказались: ${stats.declined}
Сегодня: ${stats.today}
За неделю: ${stats.thisWeek}`);

        return stats;
      } catch (error) {
        console.error('Ошибка получения статистики:', error);
      }
    },

    /**
     * Очищает все логи (только для администратора)
     */
    clearLogs: function() {
      if (confirm('Вы уверены, что хотите удалить ВСЕ логи согласий? Это действие необратимо!')) {
        localStorage.removeItem('consent_logs');
        console.log('Все логи согласий удалены');
        alert('Логи очищены');
      }
    },

    /**
     * Показывает последние записи в консоли
     */
    showRecentLogs: function(count = 10) {
      try {
        const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');
        const recentLogs = logs.slice(-count);

        console.log(`Последние ${count} записей:`);
        console.table(recentLogs.map(log => ({
          Дата: new Date(log.date).toLocaleString(),
          Согласие: log.consent ? 'Да' : 'Нет',
          IP: log.ip,
          Источник: log.source
        })));

        return recentLogs;
      } catch (error) {
        console.error('Ошибка получения логов:', error);
      }
    }
  };

  // Добавляем горячие клавиши для администратора (Ctrl+Shift+C)
  document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.code === 'KeyC') {
      console.log('🔧 Административная панель согласий:');
      console.log('ConsentAdmin.exportLogs() - экспорт в JSON');
      console.log('ConsentAdmin.exportCSV() - экспорт в CSV');
      console.log('ConsentAdmin.showStats() - статистика');
      console.log('ConsentAdmin.showRecentLogs(10) - последние записи');
      console.log('ConsentAdmin.clearLogs() - очистить все логи');
    }
  });

  /**
   * Функции для модального окна уведомления об обновлении
   * Позволяют показывать и скрывать уведомление о новой версии сайта
   */
  let currentWorker = null;

  /**
   * Показывает модальное окно с уведомлением об обновлении
   */
  window.showUpdateModal = function(worker) {
    currentWorker = worker;
    const modal = document.getElementById('updateModal');
    if (modal) {
      // Показываем модальное окно с анимацией
      modal.classList.remove('hidden');
      modal.classList.add('scale-100');
      modal.classList.remove('scale-95', 'opacity-0');
    }
  };

  /**
   * Закрывает модальное окно с уведомлением об обновлении
   */
  window.closeUpdateModal = function() {
    const modal = document.getElementById('updateModal');
    if (modal) {
      // Скрываем модальное окно с анимацией
      modal.classList.add('scale-95', 'opacity-0');
      setTimeout(() => {
        modal.classList.add('hidden');
        modal.classList.remove('scale-100');
      }, 300);
    }
  };

  /**
   * Обновляет сайт сейчас
   */
  window.updateNow = function() {
    console.log('Нажата кнопка Обновить сейчас');
    // Сначала закрываем модальное окно
    window.closeUpdateModal();

    if (currentWorker) {
      console.log('Отправляем сообщение Service Worker для активации');
      // Отправляем сообщение Service Worker для активации
      currentWorker.postMessage({ type: 'SKIP_WAITING' });
      // Перезагружаем текущую страницу с небольшой задержкой
      setTimeout(() => {
        // Используем location.href вместо location.reload() для предотвращения открытия новой вкладки
        window.location.href = window.location.href;
      }, 300);
    } else {
      console.log('Нет доступного Service Worker для обновления');
      // Если нет Service Worker, просто перезагружаем текущую страницу
      window.location.href = window.location.href;
    }
  };

  // Добавляем обработчик клика вне модального окна демо-версии
  const demoModal = document.getElementById('demoModal');
  if (demoModal) {
    demoModal.addEventListener('click', function(event) {
      // Проверяем, что клик был не по содержимому модального окна
      const modalContent = demoModal.querySelector('.bg-white');
      if (event.target === this && !modalContent.contains(event.target)) {
        window.closeDemoModal();
      }
    });
  }

  // Добавляем обработчик клика вне модального окна выбора тарифа
  const pricingModal = document.getElementById('pricingModal');
  if (pricingModal) {
    pricingModal.addEventListener('click', function(event) {
      // Проверяем, что клик был не по содержимому модального окна
      const modalContent = pricingModal.querySelector('.bg-white');
      if (event.target === this && !modalContent.contains(event.target)) {
        window.closePricingModal();
      }
    });
  }

  /**
   * Обработчики для внешних ссылок на Telegram бота добавляются в modalManager.init()
   */

  /**
   * Переключатель темы (светлая/темная)
   * Позволяет пользователю выбрать предпочтительную тему оформления сайта
   */
  const themeToggle = document.getElementById('theme-toggle'); // Кнопка переключения темы
  const htmlElement = document.documentElement;              // Корневой элемент HTML для установки класса темы

  // Проверяем, что кнопка переключения темы существует на странице
  if (themeToggle) {
    // Добавляем обработчик клика на кнопку переключения темы
    themeToggle.addEventListener('click', function() {
      // Переключаем класс 'dark' для изменения темы
      htmlElement.classList.toggle('dark');

      // Сохраняем выбранную тему в localStorage для сохранения между сессиями
      const isDark = htmlElement.classList.contains('dark');
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
    });
  }

  /**
   * Переключатели для секции тарифов
   * Обрабатывают переключение между периодами подписки и вариантами размещения
   */

  // Переключатель периодов подписки
  const periodButtons = document.querySelectorAll('.period-btn');
  const periodOptions = document.querySelectorAll('.period-option');

  if (periodButtons.length > 0) {
    // Добавляем обработчики клика на кнопки периодов
    periodButtons.forEach(button => {
      button.addEventListener('click', function() {
        // Получаем выбранный период
        const selectedPeriod = this.getAttribute('data-period');

        // Снимаем активность со всех кнопок
        periodButtons.forEach(btn => btn.classList.remove('active'));

        // Делаем текущую кнопку активной
        this.classList.add('active');

        // Скрываем все варианты периодов
        periodOptions.forEach(option => option.classList.remove('active'));

        // Показываем выбранный вариант периода
        const activeOption = document.querySelector(`.period-option[data-period="${selectedPeriod}"]`);
        if (activeOption) {
          activeOption.classList.add('active');
        }
      });
    });
  }

  // Переключатель вариантов размещения (Ваш сервер / Наш сервер)
  const paymentToggle = document.getElementById('paymentToggle');

  if (paymentToggle) {
    // Находим все элементы, связанные с переключением вариантов размещения
    const paymentLabels = document.querySelectorAll('.payment-toggle-label');

    // Добавляем обработчик изменения состояния переключателя
    paymentToggle.addEventListener('change', function() {
      // Переключаем активный вариант оплаты в каждом периоде
      periodOptions.forEach(periodOption => {
        const options = periodOption.querySelectorAll('.payment-option');
        options.forEach(option => {
          option.classList.toggle('active');
        });
      });

      // Переключаем активный лейбл
      paymentLabels.forEach(label => {
        label.classList.toggle('active');
      });
    });

    // Добавляем обработчики клика на лейблы
    paymentLabels.forEach((label, index) => {
      label.addEventListener('click', function() {
        // Устанавливаем состояние переключателя в зависимости от выбранного лейбла
        paymentToggle.checked = index === 1; // Если второй лейбл (Наш сервер), то устанавливаем checked=true

        // Вызываем событие change для обновления интерфейса
        paymentToggle.dispatchEvent(new Event('change'));
      });
    });
  }

  /**
   * Предотвращение нежелательного перескакивания при прокрутке страницы
   * Реализует мягкий подход к предотвращению скачков при достижении конца страницы
   */
  let lastScrollTop = 0;  // Переменная для отслеживания последней позиции прокрутки
  let scrollTimeout;     // Таймер для отслеживания окончания прокрутки

  /**
   * Обработчик события прокрутки страницы
   * Отслеживает позицию прокрутки и предотвращает нежелательные скачки
   */
  window.addEventListener('scroll', function() {
    // Получаем текущую позицию прокрутки
    const scrollTop = window.scrollY || document.documentElement.scrollTop;

    // Определяем направление прокрутки (вниз или вверх)
    const isScrollingDown = scrollTop > lastScrollTop;

    // Сохраняем текущую позицию прокрутки для следующего сравнения
    lastScrollTop = scrollTop;

    // Очищаем предыдущий таймаут для предотвращения наложения
    clearTimeout(scrollTimeout);

    // Проверяем, достигли ли мы конца страницы
    const scrollHeight = document.documentElement.scrollHeight; // Полная высота страницы
    const clientHeight = document.documentElement.clientHeight; // Высота видимой области

    // Если мы достигли конца страницы и продолжаем прокручивать вниз
    if (scrollTop + clientHeight >= scrollHeight - 20 && isScrollingDown) {
      // Временно отключаем плавную прокрутку для предотвращения скачков
      document.documentElement.style.scrollBehavior = 'auto';
    }

    // Устанавливаем таймер для восстановления плавной прокрутки после завершения
    scrollTimeout = setTimeout(function() {
      // Возвращаем плавную прокрутку после окончания скролла
      document.documentElement.style.scrollBehavior = 'smooth';
    }, 100); // Задержка в 100 миллисекунд
  }, { passive: true }); // Пассивный обработчик для повышения производительности
});
