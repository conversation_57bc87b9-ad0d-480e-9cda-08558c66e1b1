{"name": "bot.revive-it.ru", "version": "1.2.6", "description": "", "main": "index.js", "dependencies": {"ansi-regex": "^6.1.0", "ansi-styles": "^6.2.1", "any-promise": "^1.3.0", "anymatch": "^3.1.3", "aos": "^2.3.4", "arg": "^5.0.2", "balanced-match": "^1.0.2", "binary-extensions": "^2.3.0", "brace-expansion": "^2.0.1", "braces": "^3.0.3", "camelcase-css": "^2.0.1", "chokidar": "^3.6.0", "color-convert": "^2.0.1", "color-name": "^1.1.4", "commander": "^4.1.1", "cross-spawn": "^7.0.6", "cssesc": "^3.0.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "fast-glob": "^3.3.3", "fastq": "^1.19.1", "fill-range": "^7.1.1", "foreground-child": "^3.3.1", "function-bind": "^1.1.2", "glob": "^10.4.5", "glob-parent": "^6.0.2", "hasown": "^2.0.2", "is-binary-path": "^2.1.0", "is-core-module": "^2.16.1", "is-extglob": "^2.1.1", "is-fullwidth-code-point": "^3.0.0", "is-glob": "^4.0.3", "is-number": "^7.0.0", "isexe": "^2.0.0", "jackspeak": "^3.4.3", "jiti": "^1.21.7", "lilconfig": "^3.1.3", "lines-and-columns": "^1.2.4", "lru-cache": "^10.4.3", "merge2": "^1.4.1", "micromatch": "^4.0.8", "minimatch": "^9.0.5", "minipass": "^7.1.2", "mz": "^2.7.0", "nanoid": "^3.3.11", "normalize-path": "^3.0.0", "object-assign": "^4.1.1", "object-hash": "^3.0.0", "package-json-from-dist": "^1.0.1", "path-key": "^3.1.1", "path-parse": "^1.0.7", "path-scurry": "^1.11.1", "picocolors": "^1.1.1", "picomatch": "^2.3.1", "pify": "^2.3.0", "pirates": "^4.0.7", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.2", "postcss-nested": "^6.2.0", "postcss-selector-parser": "^6.1.2", "postcss-value-parser": "^4.2.0", "queue-microtask": "^1.2.3", "read-cache": "^1.0.0", "readdirp": "^3.6.0", "resolve": "^1.22.10", "reusify": "^1.1.0", "run-parallel": "^1.2.0", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "signal-exit": "^4.1.0", "source-map-js": "^1.2.1", "string-width": "^5.1.2", "string-width-cjs": "^4.2.3", "strip-ansi": "^7.1.0", "strip-ansi-cjs": "^6.0.1", "sucrase": "^3.35.0", "supports-preserve-symlinks-flag": "^1.0.0", "thenify": "^3.3.1", "thenify-all": "^1.6.0", "to-regex-range": "^5.0.1", "ts-interface-checker": "^0.1.13", "util-deprecate": "^1.0.2", "which": "^2.0.2", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "^7.0.0", "yaml": "^2.7.1"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "cssnano": "^7.0.6", "html-webpack-plugin": "^5.6.3", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "serve": "^14.2.4", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "webpack": "^5.99.5", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "cross-env NODE_ENV=development webpack serve --no-open", "update-version": "node version-manager.js update", "increment-version": "node version-manager.js increment", "set-version": "node version-manager.js set", "version-help": "node version-manager.js help", "build": "npm run update-version && cross-env NODE_ENV=production webpack --mode production", "serve": "npx serve ./dist --no-clipboard"}, "keywords": [], "author": "", "license": "ISC", "type": "module"}