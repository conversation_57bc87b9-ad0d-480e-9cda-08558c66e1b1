# 📋 Техническая документация системы согласий

## 🏗️ Архитектура системы

### Компоненты системы:
1. **Модальное окно согласия** - UI компонент для получения согласия
2. **Менеджер согласий** - JavaScript класс для управления логикой
3. **Локальное хранилище** - localStorage для сохранения данных
4. **Административная панель** - инструменты для управления данными

### Поток данных:
```
Пользователь → Модальное окно → Менеджер согласий → localStorage → Административная панель
```

## 🔧 Техническая реализация

### JavaScript API

#### Основной класс: `privacyConsentManager`

```javascript
const privacyConsentManager = {
  // Константы
  CONSENT_KEY: 'privacy_consent_given',
  CONSENT_DATA_KEY: 'privacy_consent_data',
  
  // Основные методы
  hasConsent(),              // Проверка наличия согласия
  getUserIP(),               // Получение IP адреса
  getSource(),               // Получение источника перехода
  saveConsentData(consent),  // Сохранение данных согласия
  logConsentLocally(data),   // Локальное логирование
  showConsentModal(),        // Показ модального окна
  hideConsentModal(),        // Скрытие модального окна
  init(),                    // Инициализация системы
  initEventHandlers(),       // Инициализация обработчиков
  initScrollToEndDetection() // Отслеживание прокрутки до конца
}
```

#### Административный API: `ConsentAdmin`

```javascript
window.ConsentAdmin = {
  exportLogs(),              // Экспорт в JSON
  exportCSV(),               // Экспорт в CSV
  showStats(),               // Статистика
  showRecentLogs(count),     // Последние записи
  clearLogs(),               // Очистка логов
  help()                     // Справка
}
```

### Структуры данных

#### Запись согласия:
```javascript
{
  id: "1704067200000abc123",           // Уникальный ID
  timestamp: "2024-01-01T00:00:00Z",   // Время создания записи
  consent: true,                       // Решение пользователя
  ip: "***********",                  // IP адрес
  date: "2024-01-01T00:00:00Z",        // Дата согласия
  source: "https://google.com",        // Источник перехода
  userAgent: "Mozilla/5.0...",         // User Agent браузера
  page: "https://bot.revive-it.ru/"    // Страница согласия
}
```

#### Ключи localStorage:
- `privacy_consent_given`: "true" | "false" | null
- `privacy_consent_data`: JSON строка с данными последнего согласия
- `consent_logs`: JSON массив всех записей согласий

## 🎨 UI компоненты

### Модальное окно согласия

#### HTML структура:
```html
<div id="privacyConsentModal" class="fixed inset-0 z-[60] hidden bg-black bg-opacity-90">
  <div class="bg-white dark:bg-dark-dark rounded-xl max-w-lg w-full p-6">
    <h3>Согласие на обработку персональных данных</h3>
    <p>Описание...</p>
    
    <label>
      <input type="checkbox" id="privacyConsentCheckbox">
      <span>Текст согласия...</span>
    </label>
    
    <button id="privacyConsentAccept">Согласиться</button>
    <button id="privacyConsentDecline">Отказаться</button>
  </div>
</div>
```

#### CSS классы:
- `z-[60]`: высокий z-index для отображения поверх всего
- `bg-opacity-90`: полупрозрачный фон
- `disabled:opacity-50`: стили для неактивной кнопки
- `dark:bg-dark-dark`: поддержка темной темы

### Адаптивность:
- Мобильные устройства: `flex-col` для вертикального расположения кнопок
- Планшеты и ПК: `sm:flex-row` для горизонтального расположения

## 🔄 Жизненный цикл согласия

### 1. Инициализация (при загрузке страницы):
```javascript
// Проверка существующего согласия
if (!privacyConsentManager.hasConsent()) {
  // Умная логика показа в зависимости от страницы
  if (window.location.pathname.includes('privacy.html')) {
    // На странице политики - после прокрутки до конца
    privacyConsentManager.initScrollToEndDetection();
  } else {
    // На остальных страницах - через 1 секунду
    setTimeout(() => {
      privacyConsentManager.showConsentModal();
    }, 1000);
  }
}
```

### 2. Взаимодействие с пользователем:
```javascript
// Активация кнопки при установке галочки
checkbox.addEventListener('change', function() {
  acceptBtn.disabled = !this.checked;
});

// Обработка согласия
acceptBtn.addEventListener('click', async () => {
  if (checkbox.checked) {
    await privacyConsentManager.saveConsentData(true);
    privacyConsentManager.hideConsentModal();
  }
});
```

### 3. Отслеживание прокрутки до конца (для privacy.html):
```javascript
initScrollToEndDetection: function() {
  let hasShownModal = false;

  const checkScrollPosition = () => {
    if (hasShownModal) return;

    // Получаем высоту документа и позицию прокрутки
    const documentHeight = Math.max(
      document.body.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.clientHeight,
      document.documentElement.scrollHeight,
      document.documentElement.offsetHeight
    );

    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Проверяем, достиг ли пользователь конца страницы (с отступом 100px)
    if (scrollTop + windowHeight >= documentHeight - 100) {
      hasShownModal = true;
      this.showConsentModal();
      // Удаляем обработчик после показа модального окна
      window.removeEventListener('scroll', checkScrollPosition);
    }
  };

  // Добавляем обработчик прокрутки
  window.addEventListener('scroll', checkScrollPosition);

  // Также проверяем при загрузке (если страница короткая)
  setTimeout(() => {
    checkScrollPosition();
  }, 1000);
}
```

### 4. Сохранение данных:
```javascript
async saveConsentData(consent) {
  const ip = await this.getUserIP();
  const consentData = {
    consent,
    ip,
    date: new Date().toISOString(),
    source: this.getSource(),
    userAgent: navigator.userAgent,
    page: window.location.href
  };

  // Сохранение в localStorage
  localStorage.setItem(this.CONSENT_KEY, consent.toString());
  localStorage.setItem(this.CONSENT_DATA_KEY, JSON.stringify(consentData));

  // Добавление в лог
  this.logConsentLocally(consentData);
}
```

## 📊 Система логирования

### Локальное логирование:
```javascript
logConsentLocally(consentData) {
  const existingLogs = JSON.parse(localStorage.getItem('consent_logs') || '[]');
  
  const logEntry = {
    id: Date.now() + Math.random().toString(36).substr(2, 9),
    timestamp: new Date().toISOString(),
    ...consentData
  };
  
  existingLogs.push(logEntry);
  
  // Ограничение количества записей
  if (existingLogs.length > 1000) {
    existingLogs.splice(0, existingLogs.length - 1000);
  }
  
  localStorage.setItem('consent_logs', JSON.stringify(existingLogs));
}
```

### Получение IP адреса:
```javascript
async getUserIP() {
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip;
  } catch (error) {
    console.log('Не удалось получить IP адрес:', error);
    return 'unknown';
  }
}
```

## 📤 Система экспорта

### JSON экспорт:
```javascript
exportLogs() {
  const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');
  const dataStr = JSON.stringify(logs, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `consent-logs-${new Date().toISOString().split('T')[0]}.json`;
  link.click();
}
```

### CSV экспорт (для Excel):
```javascript
exportCSV() {
  const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');
  
  // BOM для корректного отображения в Excel
  const BOM = '\uFEFF';
  const headers = ['ID', 'Дата', 'Время', 'Согласие', 'IP', 'Источник'];
  const csvRows = [headers.join(';')]; // Используем ; для Excel
  
  logs.forEach(log => {
    const date = new Date(log.date);
    const row = [
      log.id,
      date.toLocaleDateString('ru-RU'),
      date.toLocaleTimeString('ru-RU'),
      log.consent ? 'Согласие' : 'Отказ',
      log.ip,
      `"${log.source.replace(/"/g, '""')}"`
    ];
    csvRows.push(row.join(';'));
  });
  
  const csvContent = BOM + csvRows.join('\n');
  const dataBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `consent-logs-${new Date().toISOString().split('T')[0]}.csv`;
  link.click();
}
```

## 🔧 Конфигурация и настройки

### Основные параметры:
```javascript
// Задержка показа модального окна (мс)
const MODAL_DELAY = 1000;

// Максимальное количество записей в логе
const MAX_LOG_ENTRIES = 1000;

// Ключи localStorage
const CONSENT_KEY = 'privacy_consent_given';
const CONSENT_DATA_KEY = 'privacy_consent_data';
const LOGS_KEY = 'consent_logs';
```

### Настройка внешних сервисов:
```javascript
// API для получения IP адреса
const IP_API_URL = 'https://api.ipify.org?format=json';

// Альтернативные API (если основной недоступен)
const FALLBACK_IP_APIS = [
  'https://ipapi.co/json/',
  'https://httpbin.org/ip'
];
```

## 🛡️ Безопасность

### Защита от XSS:
- Экранирование кавычек в CSV экспорте
- Использование `textContent` вместо `innerHTML`
- Валидация данных перед сохранением

### Ограничения:
- Максимальный размер лога (1000 записей)
- Таймауты для внешних API запросов
- Обработка ошибок сети

### Конфиденциальность:
- Данные хранятся только локально
- Нет передачи на внешние серверы (кроме получения IP)
- Возможность отключения получения IP

## 🧪 Тестирование

### Автоматические тесты:
```javascript
// Проверка инициализации
console.assert(typeof privacyConsentManager === 'object');
console.assert(typeof ConsentAdmin === 'object');

// Проверка методов
console.assert(typeof privacyConsentManager.hasConsent === 'function');
console.assert(typeof ConsentAdmin.exportLogs === 'function');
```

### Ручное тестирование:
1. Очистка localStorage: `localStorage.clear()`
2. Обновление страницы
3. Проверка появления модального окна
4. Тестирование согласия/отказа
5. Проверка экспорта данных

## 📱 Совместимость

### Поддерживаемые браузеры:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Требуемые API:
- localStorage
- fetch API
- Blob API
- URL.createObjectURL
- JSON.parse/stringify

### Graceful degradation:
- При отсутствии localStorage система не работает
- При отсутствии fetch API IP не определяется
- При отсутствии Blob API экспорт недоступен
