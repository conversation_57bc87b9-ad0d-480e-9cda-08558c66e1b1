# 🤖 Telegram Support Bot - Официальный сайт

Современный адаптивный сайт для сервиса поддержки клиентов в Telegram с интегрированной системой согласий на обработку персональных данных.

![Telegram Support Bot](./src/assets/img/first_run.png)

## Описание проекта

Официальный сайт для сервиса "Telegram Support Bot" - системы автоматизированной поддержки клиентов в Telegram. Сайт представляет возможности бота, демонстрирует скриншоты интерфейса, предлагает различные тарифные планы и включает полноценную систему согласий на обработку персональных данных.

## 🔒 Система согласий с политикой конфиденциальности

### Особенности системы:
- ✅ **Умное модальное окно**:
  - Обычные страницы: показ через 1 секунду
  - Страница политики: показ после прокрутки до конца
- ✅ **Галочка согласия** (обязательная для активации кнопки)
- ✅ **Локальное логирование** без внешних серверов
- ✅ **Административная панель** для экспорта данных
- ✅ **Соответствие GDPR** и российскому законодательству

### Что фиксируется:
- IP адрес пользователя
- Дата и время согласия/отказа
- Источник перехода (referrer)
- Страница на которой было дано согласие
- User Agent браузера
- Решение пользователя (согласие/отказ)

### Административная панель:
```javascript
// Доступ через консоль браузера (F12)
ConsentAdmin.showStats()      // Статистика согласий
ConsentAdmin.exportLogs()     // Экспорт в JSON
ConsentAdmin.exportCSV()      // Экспорт в CSV для Excel
ConsentAdmin.help()           // Справка по командам
```

## Технологии

- **Webpack** - для сборки проекта
- **TailwindCSS** - для стилизации
- **AOS (Animate On Scroll)** - для анимаций при прокрутке
- **Font Awesome** - для иконок
- **JavaScript (ES6+)** - для интерактивности

## Особенности

- **Адаптивный дизайн** - корректное отображение на мобильных устройствах, планшетах и ПК
- **Темная тема** - поддержка светлой и темной темы с автоматическим определением системных настроек
- **Оптимизированные изображения** - галерея с выровненными изображениями и центрированными подписями
- **Анимации** - плавные анимации при прокрутке и взаимодействии с элементами
- **Модальная галерея** - просмотр скриншотов в увеличенном виде
- **Контрастные цвета** - улучшенная цветовая гамма для лучшей читаемости
- **Система согласий** - полноценная система согласий с политикой конфиденциальности
- **Локальное логирование** - сохранение данных согласий без внешних серверов

## Структура проекта

```
├── dist/               # Скомпилированные файлы для продакшена
├── src/                # Исходные файлы
│   ├── assets/         # Статические ресурсы
│   │   └── img/        # Изображения
│   ├── index.html      # Главная страница
│   ├── docs.html       # Страница документации
│   ├── terms.html      # Условия использования
│   ├── privacy.html    # Политика конфиденциальности
│   ├── index.js        # JavaScript логика + система согласий
│   └── styles.css      # Стили с использованием TailwindCSS
├── test-consent.html   # Тестовая страница системы согласий
├── webpack.config.js   # Конфигурация Webpack
├── tailwind.config.js  # Конфигурация TailwindCSS
├── postcss.config.js   # Конфигурация PostCSS
├── version-manager.js  # Скрипт управления версиями проекта
├── .husky/             # Git-хуки для автоматизации процессов
├── docs/               # Документация
│   ├── QUICK_START.md      # Быстрый старт системы согласий
│   ├── HOSTING_GUIDE.md    # Руководство по размещению на хостинге
│   ├── CONSENT_DOCUMENTATION.md  # Техническая документация
│   ├── TESTING_PRIVACY_SCROLL.md  # Тестирование прокрутки на странице политики
│   └── CHANGELOG_CONSENT.md    # Changelog системы согласий
└── package.json        # Зависимости и скрипты
```

## Установка и запуск

> **⚡ Для быстрого развертывания на хостинге:** см. [QUICK_DEPLOY.md](QUICK_DEPLOY.md)

### Требования

- Node.js (версия 14 или выше)
- npm (версия 6 или выше)

### Установка зависимостей

```bash
npm install
```

### Запуск сервера разработки

```bash
npm run dev
```

Сервер будет доступен по адресу: http://localhost:3000

### Сборка для продакшена

```bash
npm run build
```

Скомпилированные файлы будут доступны в директории `dist/`.

### Размещение на хостинге

Для размещения на хостинге без поддержки Node.js:

```bash
# 1. Соберите проект
npm run build

# 2. Загрузите содержимое папки dist/ на ваш хостинг
# 3. Готово! Сайт работает без дополнительных серверов
```

**Подробная инструкция:** см. файл `docs/HOSTING_GUIDE.md`

### Управление версиями

В проекте реализовано ручное управление версиями с помощью скрипта `version-manager.js`:

- **Установка конкретной версии** - возможность задать любую версию вручную
- **Увеличение версии** - увеличение патч-версии на единицу (1.0.x -> 1.0.x+1)
- **Обновление версии при сборке** - при сборке проекта версия и дата сборки обновляются в файлах
- **Синхронизация версий** - версия автоматически синхронизируется между всеми файлами (package.json, package-lock.json, index.js, service-worker.js)

Доступные команды для управления версиями:

```bash
# Установка конкретной версии (например, 1.1.0)
npm run set-version 1.1.0

# Увеличение версии (патч-версия +1)
npm run increment-version

# Обновление файлов до текущей версии без увеличения
npm run update-version

# Показать справку по управлению версиями
npm run version-help
```

Версия отображается в футере сайта в формате `X.Y.Z (YYYY-MM-DD)`, где:
- X.Y.Z - семантическая версия (major.minor.patch)
- YYYY-MM-DD - дата сборки

## Функциональность

- **Переключение темы** - возможность переключения между светлой и темной темой
- **Мобильное меню** - адаптивное меню для мобильных устройств
- **Модальная галерея** - просмотр скриншотов в увеличенном виде
- **Плавная прокрутка** - плавная прокрутка к разделам при клике на ссылки в меню
- **Анимации при прокрутке** - элементы плавно появляются при прокрутке страницы
- **Ручное управление версией** - возможность установки любой версии через командную строку
- **Отображение версии** - отображение текущей версии сайта в футере
- **Система согласий** - модальное окно согласия с политикой конфиденциальности
- **Административная панель** - управление данными согласий через консоль браузера
- **Экспорт данных** - экспорт логов согласий в JSON и CSV форматы

## Разделы сайта

1. **Главная** - краткое описание сервиса и призыв к действию
2. **Почему Мы** - преимущества и особенности решения
3. **Возможности** - основные функции и преимущества бота
4. **Скриншоты** - галерея скриншотов интерфейса бота
5. **Тарифы** - доступные тарифные планы
6. **Документация** - подробная документация и FAQ
7. **Политика конфиденциальности** - политика обработки персональных данных
8. **Условия использования** - коммерческая лицензия и условия

## 🧪 Тестирование системы согласий

### Тестовая страница:
Откройте `test-consent.html` в браузере для тестирования всех функций:
- Показ модального окна согласия
- Экспорт логов в JSON и CSV
- Просмотр статистики согласий
- Очистка данных
- Переключение темы

### Проверка на реальном сайте:
1. Очистите localStorage: `localStorage.clear()`
2. Обновите страницу
3. Через 1 секунду появится модальное окно согласия

## 📊 Документация

- **[QUICK_START.md](docs/QUICK_START.md)** - Быстрый старт и основные функции
- **[HOSTING_GUIDE.md](docs/HOSTING_GUIDE.md)** - Подробное руководство по размещению на хостинге
- **[CONSENT_DOCUMENTATION.md](docs/CONSENT_DOCUMENTATION.md)** - Техническая документация системы согласий
- **[TESTING_PRIVACY_SCROLL.md](docs/TESTING_PRIVACY_SCROLL.md)** - Тестирование прокрутки на странице политики
- **[CHANGELOG_CONSENT.md](docs/CHANGELOG_CONSENT.md)** - Changelog системы согласий

## 📄 Лицензия

Коммерческая лицензия. Все права защищены.

**Правообладатель:** Засорин Кирилл Глебович
**Email:** <EMAIL>
**Telegram:** @rev_support_bot
**Сайт:** https://bot.revive-it.ru

---

**Сделано с ❤️ в [Revive-IT](https://revive-it.ru)**
