<!--?xml version="1.0" encoding="UTF-8" standalone="no"?-->
<svg xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" inkscape:version="1.0 (4035a4fb49, 2020-05-01)" sodipodi:docname="logo2.svg" id="svg15" width="361.43359375" height="103.79760131835937" version="1.1" style="">
  <metadata id="metadata19">
    <rdf:rdf>
      <cc:work rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type>
        <dc:title></dc:title>
      </cc:work>
    </rdf:rdf>
  </metadata>
  <sodipodi:namedview inkscape:current-layer="svg15" inkscape:window-maximized="1" inkscape:window-y="0" inkscape:window-x="0" inkscape:cy="51.65298" inkscape:cx="151.82868" inkscape:zoom="2.0340119" inkscape:snap-global="true" showgrid="false" id="namedview17" inkscape:window-height="872" inkscape:window-width="1569" inkscape:pageshadow="2" inkscape:pageopacity="0" guidetolerance="10" gridtolerance="10" objecttolerance="10" borderopacity="1" bordercolor="#666666" pagecolor="#ffffff"></sodipodi:namedview>
  <defs id="SvgjsDefs1006">
    <path id="rect23" height="51.243304" width="249.82886" d="M100.93264 30.263388 L350.7615 30.263388 L350.7615 81.506692 L100.93264 81.506692 Z" class="tqxrSDci_0"></path>
  </defs>
  <g style="opacity:1" fill="none" stroke="#dd0b0b" stroke-width="1px" transform="translate(98.00359964370728,-158.36619873046874)" name="main_text" rel="mainfill" id="SvgjsG1007"></g>
  <g fill="none" stroke="#004080" stroke-width="1px" transform="translate(-14.083204650878907,-5.700001144409179) scale(1.2)" name="symbol" rel="mainfill" id="SvgjsG1008">
    <path id="path6" style="fill: none; stroke: #004080; stroke-width: 1px" fill="none" d="M80.374,40.589h-1.309c-1.559,0-2.896,0.927-3.516,2.254c-3.372-11.634-13.975-20.218-26.604-20.547v-5.291  c3.021-0.391,5.365-2.974,5.365-6.099c0-3.395-2.763-6.156-6.158-6.156c-3.395,0-6.156,2.762-6.156,6.156  c0,3.155,2.387,5.76,5.449,6.112v5.277c-12.85,0.335-23.605,9.214-26.777,21.158c-0.453-1.646-1.946-2.865-3.734-2.865h-1.309  c-2.145,0-3.889,1.745-3.889,3.889v12.557c0,2.145,1.744,3.889,3.889,3.889h1.309c1.788,0,3.282-1.219,3.734-2.865  c1.942,7.385,6.792,13.726,13.637,17.544c-4.13,3.843-6.479,9.212-6.479,14.896v0.75h40.74V90.5c0-5.684-2.35-11.053-6.48-14.896  c6.65-3.712,11.427-9.799,13.474-16.915c0.624,1.316,1.955,2.235,3.506,2.235h1.309c2.145,0,3.889-1.745,3.889-3.889V44.479  C84.263,42.334,82.519,40.589,80.374,40.589z M19.323,57.036c0,1.317-1.071,2.389-2.389,2.389h-1.309  c-1.317,0-2.389-1.072-2.389-2.389V44.479c0-1.317,1.071-2.389,2.389-2.389h1.309c1.317,0,2.389,1.072,2.389,2.389V57.036z   M43.497,10.906c0-2.567,2.089-4.656,4.656-4.656c2.568,0,4.658,2.089,4.658,4.656s-2.09,4.656-4.658,4.656  C45.586,15.563,43.497,13.474,43.497,10.906z M67.052,89.75H29.341c0.026-0.67,0.092-1.333,0.188-1.989h37.336  C66.96,88.417,67.025,89.081,67.052,89.75z M66.574,86.261H29.818c0.167-0.727,0.366-1.445,0.616-2.144h35.522  C66.208,84.816,66.407,85.534,66.574,86.261z M65.335,82.618H31.057c1.087-2.362,2.653-4.5,4.65-6.261  c0.187,0.091,0.377,0.17,0.564,0.257c0.235,0.109,0.47,0.22,0.708,0.323c0.271,0.117,0.545,0.224,0.818,0.332  c0.235,0.093,0.47,0.188,0.708,0.275c0.281,0.103,0.565,0.194,0.85,0.287c0.234,0.077,0.468,0.158,0.705,0.228  c0.291,0.087,0.586,0.162,0.88,0.24c0.233,0.062,0.466,0.127,0.701,0.183c0.304,0.071,0.61,0.13,0.917,0.191  c0.229,0.046,0.457,0.098,0.688,0.138c0.324,0.057,0.651,0.099,0.978,0.144c0.217,0.03,0.433,0.067,0.65,0.092  c0.366,0.042,0.735,0.068,1.104,0.097c0.183,0.014,0.363,0.035,0.546,0.046c0.554,0.031,1.11,0.048,1.671,0.048  s1.117-0.017,1.671-0.048c0.181-0.011,0.358-0.032,0.538-0.045c0.372-0.028,0.744-0.055,1.112-0.097  c0.216-0.024,0.429-0.061,0.643-0.091c0.329-0.045,0.659-0.088,0.985-0.145c0.229-0.04,0.455-0.091,0.683-0.137  c0.309-0.062,0.617-0.121,0.924-0.193c0.232-0.055,0.461-0.12,0.692-0.181c0.297-0.078,0.595-0.154,0.889-0.242  c0.233-0.069,0.464-0.149,0.695-0.225c0.287-0.094,0.575-0.187,0.86-0.291c0.232-0.084,0.462-0.178,0.692-0.269  c0.279-0.11,0.56-0.219,0.836-0.339c0.229-0.099,0.457-0.207,0.685-0.312c0.194-0.09,0.391-0.172,0.583-0.266  C62.682,78.117,64.249,80.256,65.335,82.618z M60.452,74.795c-3.808,1.952-7.932,2.942-12.256,2.942  c-1.081,0-2.15-0.062-3.204-0.185c-0.666-0.078-1.324-0.189-1.979-0.316c-1.935-0.375-3.817-0.955-5.633-1.754  c-0.186-0.082-0.367-0.176-0.552-0.263c-0.298-0.139-0.598-0.273-0.892-0.424c-9.081-4.63-14.722-13.84-14.722-24.038  c0-14.877,12.104-26.98,26.98-26.98s26.98,12.103,26.98,26.98C75.177,60.953,69.535,70.164,60.452,74.795z M82.763,57.036  c0,1.317-1.071,2.389-2.389,2.389h-1.309c-1.317,0-2.389-1.072-2.389-2.389v-6.279v-6.278c0-1.317,1.071-2.389,2.389-2.389h1.309  c1.317,0,2.389,1.072,2.389,2.389V57.036z" class="tqxrSDci_1"></path>
    <path id="path8" style="fill: none; stroke: #004080; stroke-width: 1px" fill="none" d="M44.48,45.787h1.5c0-4.645-3.778-8.424-8.423-8.424s-8.424,3.779-8.424,8.424h1.5c0-3.818,3.106-6.924,6.924-6.924  S44.48,41.969,44.48,45.787z" class="tqxrSDci_2"></path>
    <path id="path10" style="fill: none; stroke: #004080; stroke-width: 1px" fill="none" d="M60.143,37.363c-4.646,0-8.425,3.779-8.425,8.424h1.5c0-3.818,3.106-6.924,6.925-6.924c3.817,0,6.924,3.106,6.924,6.924h1.5  C68.566,41.142,64.787,37.363,60.143,37.363z" class="tqxrSDci_3"></path>
    <path id="path12" style="fill: none; stroke: #004080; stroke-width: 1px" fill="none" d="M48.154,71.971c-3.817,0-6.924-3.106-6.924-6.924h-1.5c0,4.645,3.779,8.424,8.424,8.424s8.424-3.779,8.424-8.424h-1.5  C55.078,68.865,51.972,71.971,48.154,71.971z" class="tqxrSDci_4"></path>
  </g>
  <text style="font-style:normal;font-weight:normal;font-size:40px;line-height:1.25;font-family:sans-serif;white-space:pre;shape-inside:url(#rect23);fill:none;fill-opacity:1;stroke:#004080;stroke-width:1px" id="text21" xml:space="preserve"><tspan x="100.93359" y="65.654297"><tspan style="fill:none;fill-opacity:1;stroke:#004080;stroke-width:1px">Revive -</tspan><tspan style="fill:#dd0b0b;fill-opacity:1;"> IT</tspan></tspan></text>
<style data-made-with="vivus-instant">.tqxrSDci_0{stroke-dasharray:603 605;stroke-dashoffset:604;animation:tqxrSDci_draw_0 12200ms linear 0ms infinite,tqxrSDci_fade 12200ms linear 0ms infinite;}.tqxrSDci_1{stroke-dasharray:831 833;stroke-dashoffset:832;animation:tqxrSDci_draw_1 12200ms linear 0ms infinite,tqxrSDci_fade 12200ms linear 0ms infinite;}.tqxrSDci_2{stroke-dasharray:52 54;stroke-dashoffset:53;animation:tqxrSDci_draw_2 12200ms linear 0ms infinite,tqxrSDci_fade 12200ms linear 0ms infinite;}.tqxrSDci_3{stroke-dasharray:52 54;stroke-dashoffset:53;animation:tqxrSDci_draw_3 12200ms linear 0ms infinite,tqxrSDci_fade 12200ms linear 0ms infinite;}.tqxrSDci_4{stroke-dasharray:52 54;stroke-dashoffset:53;animation:tqxrSDci_draw_4 12200ms linear 0ms infinite,tqxrSDci_fade 12200ms linear 0ms infinite;}@keyframes tqxrSDci_draw{100%{stroke-dashoffset:0;}}@keyframes tqxrSDci_fade{0%{stroke-opacity:1;}96.72131147540983%{stroke-opacity:1;}100%{stroke-opacity:0;}}@keyframes tqxrSDci_draw_0{6.557377049180328%{stroke-dashoffset: 604}31.389074464258183%{ stroke-dashoffset: 0;}100%{ stroke-dashoffset: 0;}}@keyframes tqxrSDci_draw_1{31.389074464258183%{stroke-dashoffset: 832}65.5943265327098%{ stroke-dashoffset: 0;}100%{ stroke-dashoffset: 0;}}@keyframes tqxrSDci_draw_2{65.5943265327098%{stroke-dashoffset: 53}67.77326686880107%{ stroke-dashoffset: 0;}100%{ stroke-dashoffset: 0;}}@keyframes tqxrSDci_draw_3{67.77326686880107%{stroke-dashoffset: 53}69.95220720489233%{ stroke-dashoffset: 0;}100%{ stroke-dashoffset: 0;}}@keyframes tqxrSDci_draw_4{69.95220720489233%{stroke-dashoffset: 53}72.1311475409836%{ stroke-dashoffset: 0;}100%{ stroke-dashoffset: 0;}}</style></svg>
