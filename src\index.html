<!DOCTYPE html>
<html lang="ru">

<head>
  <!-- Предотвращение мигания темы -->
  <style>
    /* Скрываем страницу до применения темы */
    html.preload * {
      transition: none !important;
    }

    /* Стили для темной темы */
    html.dark {
      background-color: #0a1428;
      color: #fff;
    }

    /* Стили для светлой темы */
    html:not(.dark) {
      background-color: #f8fafc;
      color: #000;
    }
  </style>
  <script type="text/javascript">
    (function () {
      // Добавляем класс preload для предотвращения мигания
      document.documentElement.classList.add("preload");

      // Проверяем сохраненную тему или системные настройки
      var savedTheme = localStorage.getItem("theme");
      var prefersDark =
        window.matchMedia &&
        window.matchMedia("(prefers-color-scheme: dark)").matches;

      // Устанавливаем тему до загрузки основного контента
      if (savedTheme === "dark" || (!savedTheme && prefersDark)) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }

      // Удаляем класс preload после загрузки страницы
      window.addEventListener("load", function () {
        document.documentElement.classList.remove("preload");
      });
    })();
  </script>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- Мета-теги для управления кэшированием -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>Telegram Support Bot - Профессиональная поддержка клиентов</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
  <link rel="stylesheet" href="assets/css/fonts.css" />
</head>

<body class="bg-light dark:bg-dark-dark">
  <!-- Header -->
  <header class="bg-white dark:bg-dark shadow-md fixed w-full top-0 z-50 transition-colors duration-300">
    <div class="container">
      <nav class="flex justify-between items-center py-5">
        <div id="logo" class="mr-3">
          <a href="javascript:void(0)" class="logo-link flex flex-col items-center">
            <img src="assets/img/revive-it-logo-secondary.svg" class="img-responsive mb-1 sm:h-[32px] h-[28px] block dark:hidden" alt="Revive-IT" style="width: auto;">
            <img src="assets/img/revive-it-logo.svg" class="img-responsive mb-1 sm:h-[32px] h-[28px] hidden dark:block" alt="Revive-IT" style="width: auto;">
            <span class="text-xs sm:text-sm font-medium text-primary dark:text-primary-dark">Telegram Support Bot</span>
          </a>
        </div>
        <ul class="hidden md:flex space-x-8">
          <li>
            <a href="#for-whom"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Кому подходит</a>
          </li>
          <li>
            <a href="#why-us"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Почему Мы</a>
          </li>
          <li>
            <a href="#features"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Возможности</a>
          </li>
          <li>
            <a href="#screenshots"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Скриншоты</a>
          </li>
          <li>
            <a href="#pricing"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Тарифы</a>
          </li>
          <li>
            <a href="#contact"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Контакты</a>
          </li>
          <li>
            <a href="docs.html"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Документация</a>
          </li>
        </ul>

        <!-- Переключатель темы -->
        <div class="flex items-center space-x-4">
          <button id="theme-toggle" class="theme-switch">
            <span class="sr-only">Переключить тему</span>
            <span class="theme-switch-track">
              <span class="theme-switch-thumb">
                <svg class="theme-switch-icon-sun" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                  fill="currentColor" width="16" height="16">
                  <path
                    d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                </svg>
                <svg class="theme-switch-icon-moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                  fill="currentColor" width="16" height="16">
                  <path fill-rule="evenodd"
                    d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"
                    clip-rule="evenodd" />
                </svg>
              </span>
            </span>
          </button>

          <button
            class="md:hidden cursor-pointer mobile-menu-button p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark">
            <span class="sr-only">Открыть меню</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-dark dark:text-light" fill="none"
              viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"
                class="menu-icon-bar" />
            </svg>
          </button>
        </div>
      </nav>
    </div>
  </header>

  <!-- Мобильное меню -->
  <div
    class="nav-links hidden md:hidden fixed top-[80px] left-0 w-full bg-white dark:bg-dark-dark shadow-lg z-40 transition-all duration-300 border-t border-gray-200 dark:border-gray-700 mobile-menu-container">
    <div class="container mx-auto pt-6 pb-4 px-4">
      <div class="grid gap-3">
        <a href="#for-whom" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
          </svg>
          Кому подходит
        </a>
        <a href="#why-us" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
              clip-rule="evenodd" />
          </svg>
          Почему Мы
        </a>
        <a href="#features" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
              clip-rule="evenodd" />
          </svg>
          Возможности
        </a>
        <a href="#screenshots" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clip-rule="evenodd" />
          </svg>
          Скриншоты
        </a>
        <a href="#pricing" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path
              d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
              clip-rule="evenodd" />
          </svg>
          Тарифы
        </a>
        <a href="#contact" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
          Контакты
        </a>
        <a href="docs.html" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
          </svg>
          Документация
        </a>
      </div>
    </div>
  </div>

  <!-- Hero Section -->
  <section
    class="pt-32 pb-24 bg-gradient-to-br from-light to-blue-50 dark:from-dark-dark dark:to-dark text-center transition-colors duration-300">
    <div class="container" data-aos="fade-up">
      <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-secondary dark:text-light mb-4 md:mb-6 tracking-tight leading-tight">
        Профессиональная поддержка клиентов в Telegram
      </h1>
      <p class="text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mb-8 md:mb-10 leading-relaxed">
        Автоматизированная система обработки запросов с раздельным и удобным интерфейсом
        для операторов и пользователей. Увеличьте удовлетворенность клиентов и
        оптимизируйте работу поддержки.
      </p>
      <div class="mt-8 flex justify-center gap-4">
        <a href="#pricing" class="inline-block bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300">Попробовать демо-версию</a>
        <a href="#pricing" class="inline-block bg-transparent border-2 border-primary text-primary hover:bg-primary hover:text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300">Выбрать полную версию</a>
      </div>
    </div>
  </section>

  <!-- Кому это подходит Section -->
  <section class="section bg-blue-50 dark:bg-dark transition-colors duration-300 overflow-hidden" id="for-whom">
    <div class="container overflow-hidden">
      <div class="section-title" data-aos="fade-up">
        <h2>Кому это подходит</h2>
        <p>Telegram Support Bot поможет различным типам бизнеса улучшить коммуникацию с клиентами</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <!-- Интернет-магазины -->
        <div class="business-card" data-aos="fade-up" data-aos-delay="100">
          <div class="business-icon">
            <i class="fas fa-shopping-cart"></i>
          </div>
          <h3>Интернет-магазины</h3>
          <ul class="business-features">
            <li>Обработка запросов о статусе заказов</li>
            <li>Консультации по товарам</li>
            <li>Обработка возвратов и обменов</li>
            <li>Уведомления о скидках и акциях</li>
          </ul>
        </div>

        <!-- Сервисные компании -->
        <div class="business-card" data-aos="fade-up" data-aos-delay="200">
          <div class="business-icon">
            <i class="fas fa-tools"></i>
          </div>
          <h3>Сервисные компании</h3>
          <ul class="business-features">
            <li>Запись на услуги и консультации</li>
            <li>Техническая поддержка клиентов</li>
            <li>Ответы на часто задаваемые вопросы</li>
            <li>Обратная связь по качеству обслуживания</li>
          </ul>
        </div>

        <!-- Рестораны и доставка еды -->
        <div class="business-card" data-aos="fade-up" data-aos-delay="300">
          <div class="business-icon">
            <i class="fas fa-utensils"></i>
          </div>
          <h3>Рестораны и доставка</h3>
          <ul class="business-features">
            <li>Прием и отслеживание заказов</li>
            <li>Бронирование столиков</li>
            <li>Информация о меню и акциях</li>
            <li>Обработка жалоб и предложений</li>
          </ul>
        </div>

        <!-- Образовательные учреждения -->
        <div class="business-card" data-aos="fade-up" data-aos-delay="400">
          <div class="business-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <h3>Образование</h3>
          <ul class="business-features">
            <li>Информация о курсах и программах</li>
            <li>Ответы на вопросы студентов</li>
            <li>Расписание занятий и мероприятий</li>
            <li>Обратная связь от учащихся</li>
          </ul>
        </div>

        <!-- Туристические агентства -->
        <div class="business-card" data-aos="fade-up" data-aos-delay="100">
          <div class="business-icon">
            <i class="fas fa-plane"></i>
          </div>
          <h3>Туристические агентства</h3>
          <ul class="business-features">
            <li>Информация о турах и направлениях</li>
            <li>Бронирование и подтверждение</li>
            <li>Ответы на вопросы о визах</li>
            <li>Поддержка во время путешествия</li>
          </ul>
        </div>

        <!-- Финансовые услуги -->
        <div class="business-card" data-aos="fade-up" data-aos-delay="200">
          <div class="business-icon">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <h3>Финансовые услуги</h3>
          <ul class="business-features">
            <li>Консультации по продуктам</li>
            <li>Помощь с транзакциями</li>
            <li>Информация о статусе заявок</li>
            <li>Уведомления о платежах</li>
          </ul>
        </div>

        <!-- Медицинские учреждения -->
        <div class="business-card" data-aos="fade-up" data-aos-delay="300">
          <div class="business-icon">
            <i class="fas fa-heartbeat"></i>
          </div>
          <h3>Медицинские учреждения</h3>
          <ul class="business-features">
            <li>Запись на прием</li>
            <li>Консультации по несложным вопросам</li>
            <li>Напоминания о приеме лекарств</li>
            <li>Информация о работе клиники</li>
          </ul>
        </div>

        <!-- Недвижимость -->
        <div class="business-card" data-aos="fade-up" data-aos-delay="400">
          <div class="business-icon">
            <i class="fas fa-home"></i>
          </div>
          <h3>Недвижимость</h3>
          <ul class="business-features">
            <li>Информация об объектах</li>
            <li>Запись на просмотр</li>
            <li>Ответы на вопросы о документах</li>
            <li>Обновления о статусе сделки</li>
          </ul>
        </div>
      </div>

      <div class="text-center overflow-hidden" data-aos="fade-up">
        <a href="#pricing" class="btn inline-block">Попробовать бесплатно</a>
      </div>
    </div>
  </section>

  <!-- Why Us Section -->
  <section class="section bg-white dark:bg-dark-dark transition-colors duration-300 overflow-hidden" id="why-us">
    <div class="container overflow-hidden">
      <div class="section-title" data-aos="fade-up">
        <h2>Почему Мы</h2>
        <p>Наш Telegram Support Bot отличается простотой установки и использования</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-stretch mb-16 overflow-hidden">
        <div class="order-2 md:order-1 overflow-hidden" data-aos="fade-right">
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-900 p-4 sm:p-6 md:p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300 h-full flex flex-col overflow-hidden">
            <h3 class="text-2xl font-bold text-secondary dark:text-light mb-6">Универсальная совместимость</h3>
            <div class="space-y-6 flex-grow">
              <div class="feature-item">
                <div class="icon-container">
                  <div class="icon-wrapper">
                    <img src="assets/img/telegram-icon-light-round.svg" alt="Telegram" class="icon-light">
                    <img src="assets/img/telegram-icon-dark-round.svg" alt="Telegram" class="icon-dark">
                  </div>
                </div>
                <div class="overflow-hidden">
                  <h4 class="text-lg font-semibold text-secondary dark:text-light mb-2">Телеграм есть у каждого</h4>
                  <p class="text-gray-600 dark:text-gray-300 break-words">Сегодня Telegram установлен практически на каждом смартфоне. С аудиторией более 800 миллионов активных пользователей, это один из самых популярных мессенджеров в мире. Вашим клиентам не нужно устанавливать дополнительные приложения или создавать новые аккаунты – они могут связаться с вами мгновенно.</p>
                </div>
              </div>
              <div class="feature-item">
                <div class="icon-container">
                  <div class="icon-wrapper">
                    <img src="assets/img/laptop-icon-light-round.svg" alt="Laptop" class="icon-light">
                    <img src="assets/img/laptop-icon-dark-round.svg" alt="Laptop" class="icon-dark">
                  </div>
                </div>
                <div class="overflow-hidden">
                  <h4 class="text-lg font-semibold text-secondary dark:text-light mb-2">Запуск на любом компьютере</h4>
                  <p class="text-gray-600 dark:text-gray-300 break-words">Наш бот работает на любой архитектуре процессора (x86, x64, ARM) и на всех популярных операционных системах (Windows, macOS, Linux, FreeBSD). Вы можете запустить его даже на домашнем ПК без необходимости аренды сервера.</p>
                </div>
              </div>
              <div class="feature-item">
                <div class="icon-container">
                  <div class="icon-wrapper">
                    <img src="assets/img/server-icon-light-round.svg" alt="Server" class="icon-light">
                    <img src="assets/img/server-icon-dark-round.svg" alt="Server" class="icon-dark">
                  </div>
                </div>
                <div class="overflow-hidden">
                  <h4 class="text-lg font-semibold text-secondary dark:text-light mb-2">Защита операторов</h4>
                  <p class="text-gray-600 dark:text-gray-300 break-words">В нашей системе реализована возможность скрытия личных данных операторов. Операторы могут скрывать свой никнейм или Telegram ID от клиентов, что обеспечивает конфиденциальность и защиту персонала. Бот выступает посредником, защищая личные данные ваших сотрудников.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="order-1 md:order-2 overflow-hidden" data-aos="fade-left">
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-900 p-4 sm:p-6 md:p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300 h-full flex flex-col overflow-hidden">
            <h3 class="text-2xl font-bold text-secondary dark:text-light mb-6">Простота использования</h3>
            <div class="space-y-6 flex-grow">
              <div class="feature-item">
                <div class="icon-container">
                  <div class="icon-wrapper">
                    <img src="assets/img/file-icon-light-round.svg" alt="File" class="icon-light">
                    <img src="assets/img/file-icon-dark-round.svg" alt="File" class="icon-dark">
                  </div>
                </div>
                <div class="overflow-hidden">
                  <h4 class="text-lg font-semibold text-secondary dark:text-light mb-2">Запуск одного файла</h4>
                  <p class="text-gray-600 dark:text-gray-300 break-words">Вся система поставляется в виде одного исполняемого файла. Нет необходимости устанавливать дополнительные зависимости, настраивать сложные конфигурации или разбираться в множестве файлов. Просто скачайте, запустите и начните работу.</p>
                </div>
              </div>
              <div class="feature-item">
                <div class="icon-container">
                  <div class="icon-wrapper">
                    <img src="assets/img/shield-icon-light-round.svg" alt="Shield" class="icon-light">
                    <img src="assets/img/shield-icon-dark-round.svg" alt="Shield" class="icon-dark">
                  </div>
                </div>
                <div class="overflow-hidden">
                  <h4 class="text-lg font-semibold text-secondary dark:text-light mb-2">Без настройки файрвола</h4>
                  <p class="text-gray-600 dark:text-gray-300 break-words">Бот не требует открытия портов или настройки файрвола. Все соединения устанавливаются изнутри наружу, что позволяет работать даже за NAT или в закрытых корпоративных сетях. Это значительно упрощает установку, последующую поддержку и повышает безопасность.</p>
                </div>
              </div>
              <div class="feature-item">
                <div class="icon-container">
                  <div class="icon-wrapper">
                    <img src="assets/img/tools-icon-light-round.svg" alt="Tools" class="icon-light">
                    <img src="assets/img/tools-icon-dark-round.svg" alt="Tools" class="icon-dark">
                  </div>
                </div>
                <div class="overflow-hidden">
                  <h4 class="text-lg font-semibold text-secondary dark:text-light mb-2">Полный контроль над историей</h4>
                  <p class="text-gray-600 dark:text-gray-300 break-words">Вся история сообщений хранится в вашей собственной базе данных, а не в Telegram. Даже если клиент удалит сообщения или чат в мессенджере, у вас сохранится полная история взаимодействия. Это позволяет вам анализировать качество обслуживания, решать спорные ситуации и улучшать работу службы поддержки.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="text-center overflow-hidden" data-aos="fade-up">
        <a href="#pricing" class="btn inline-block">Попробовать</a>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="section bg-white dark:bg-dark transition-colors duration-300" id="features">
    <div class="container">
      <div class="section-title" data-aos="fade-up">
        <h2>Мощные возможности для вашего бизнеса</h2>
        <p>
          Telegram Support Bot предлагает все необходимое для эффективной
          работы службы поддержки
        </p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
          <div class="feature-icon">
            <i class="fas fa-comments"></i>
          </div>
          <h3>Управление чатами</h3>
          <p class="text-gray-700 dark:text-gray-200">
            Удобный интерфейс для операторов с возможностью просмотра активных
            чатов, архивации и подключения к диалогам.
          </p>
        </div>

        <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
          <div class="feature-icon">
            <i class="fas fa-archive"></i>
          </div>
          <h3>Архивация переписок</h3>
          <p class="text-gray-600 dark:text-gray-300">
            Все чаты автоматически сохраняются в архиве бота а не телеграмм, даже если их удалят,
            их можно скачать и проанализировать историю сообщений.
          </p>
        </div>

        <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
          <div class="feature-icon">
            <i class="fas fa-cloud-upload-alt"></i>
          </div>
          <h3>Резервное копирование</h3>
          <p class="text-gray-600 dark:text-gray-300">
            Автоматическое и ручное создание резервных копий базы данных и
            настроек с гибкими интервалами.
          </p>
        </div>

        <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
          <div class="feature-icon">
            <i class="fas fa-user-tag"></i>
          </div>
          <h3>Гибкие статусы</h3>
          <p class="text-gray-600 dark:text-gray-300">
            Операторы могут менять свой статус (онлайн/оффлайн), а клиенты
            видят текущее состояние поддержки и присвоенного им оператора.
          </p>
        </div>

        <div class="feature-card" data-aos="fade-up" data-aos-delay="500">
          <div class="feature-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <h3>Шаблоны ответов</h3>
          <p class="text-gray-600 dark:text-gray-300">
            Библиотека готовых ответов для быстрой обработки типовых запросов
            с возможностью категоризации.
          </p>
        </div>

        <div class="feature-card" data-aos="fade-up" data-aos-delay="600">
          <div class="feature-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <h3>Аналитика источников</h3>
          <p class="text-gray-600 dark:text-gray-300">
            Отслеживание откуда пришел клиент с помощью специальных ссылок и
            меток для анализа эффективности каналов.
          </p>
        </div>

        <div class="feature-card" data-aos="fade-up" data-aos-delay="700">
          <div class="feature-icon">
            <i class="fas fa-chart-pie"></i>
          </div>
          <h3>Статистика для оператора</h3>
          <p class="text-gray-600 dark:text-gray-300">
            Детальная статистика по каждому оператору: время ответа, количество решенных запросов, рейтинг удовлетворенности клиентов.
          </p>
        </div>

        <div class="feature-card" data-aos="fade-up" data-aos-delay="800">
          <div class="feature-icon">
            <i class="fas fa-columns"></i>
          </div>
          <h3>Раздельный интерфейс</h3>
          <p class="text-gray-600 dark:text-gray-300">
            Уникальный раздельный интерфейс для пользователей и операторов — клиенты общаются через простой интерфейс, а операторы используют свою многофункциональную панель.
          </p>
        </div>

        <div class="feature-card" data-aos="fade-up" data-aos-delay="900">
          <div class="feature-icon">
            <i class="fas fa-star"></i>
          </div>
          <h3>Система оценки</h3>
          <p class="text-gray-600 dark:text-gray-300">
            Возможность оценить качество и быстроту ответов оператора. Клиенты могут ставить оценки и оставлять отзывы после завершения диалога с оператором.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Screenshots Section -->
  <section class="section bg-blue-50 dark:bg-dark transition-colors duration-300" id="screenshots">
    <div class="container max-w-8xl">
      <div class="section-title" data-aos="fade-up">
        <h2>Как это выглядит</h2>
        <p>Посмотрите как выглядит Telegram Support Bot в действии на стороне оператора</p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-fr">
        <div class="screenshot-item" onclick="openModal('assets/img/first_run.png', 'Главное меню оператора')"
          data-aos="fade-up" data-aos-delay="100">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/first_run.png" alt="Главное меню оператора" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Главное меню оператора</div>
        </div>
        <div class="screenshot-item" onclick="openModal('assets/img/operator_chat.png', 'Диалог с клиентом')"
          data-aos="fade-up" data-aos-delay="200">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/operator_chat.png" alt="Диалог с клиентом" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Диалог с клиентом</div>
        </div>
        <div class="screenshot-item" onclick="openModal('assets/img/chat_archive.png', 'Архив завершенных чатов')"
          data-aos="fade-up" data-aos-delay="300">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/chat_archive.png" alt="Архив завершенных чатов" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Архив завершенных чатов</div>
        </div>
        <div class="screenshot-item" onclick="openModal('assets/img/templates.png', 'Создание шаблонов ответов')"
          data-aos="fade-up" data-aos-delay="400">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/templates.png" alt="Создание шаблонов ответов" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Создание шаблонов</div>
        </div>
        <div class="screenshot-item" onclick="openModal('assets/img/templates_2.png', 'Просмотр шаблонов ответов')"
          data-aos="fade-up" data-aos-delay="100">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/templates_2.png" alt="Просмотр шаблонов ответов" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Просмотр шаблонов</div>
        </div>
        <div class="screenshot-item" onclick="openModal('assets/img/list_backup.png', 'Управление резервными копиями')"
          data-aos="fade-up" data-aos-delay="200">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/list_backup.png" alt="Управление резервными копиями" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Управление резервными копиями</div>
        </div>
        <div class="screenshot-item" onclick="openModal('assets/img/autobackup_settings.png', 'Настройка автобэкапа')"
          data-aos="fade-up" data-aos-delay="300">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/autobackup_settings.png" alt="Настройка автобэкапа" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Настройка автобэкапа</div>
        </div>
        <div class="screenshot-item"
          onclick="openModal('assets/img/change_operator_status.png', 'Изменение статуса оператора')" data-aos="fade-up"
          data-aos-delay="400">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/change_operator_status.png" alt="Изменение статуса оператора" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Изменение статуса оператора</div>
        </div>
        <div class="screenshot-item" onclick="openModal('assets/img/backup_export.png', 'Экспорт настроек')"
          data-aos="fade-up" data-aos-delay="100">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/backup_export.png" alt="Экспорт настроек" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Экспорт настроек</div>
        </div>
        <div class="screenshot-item" onclick="openModal('assets/img/operator_stats.png', 'Статистика для операторов')"
          data-aos="fade-up" data-aos-delay="100">
          <div class="h-96 overflow-hidden bg-gray-100 dark:bg-gray-800 screenshot-image-container">
            <img src="assets/img/operator_stats.png" alt="Статистика для операторов" class="screenshot-image" />
          </div>
          <div class="screenshot-caption">Статистика для операторов</div>
        </div>

      </div>
    </div>
  </section>

  <!-- Modal Gallery -->
  <div id="imageModal"
    class="fixed inset-0 z-50 hidden bg-black bg-opacity-90 flex flex-col justify-center items-center p-4">
    <button id="closeGalleryBtn" type="button" class="absolute top-2 right-2 sm:top-4 sm:right-6 text-white text-5xl sm:text-4xl font-bold hover:text-accent-DEFAULT transition-colors cursor-pointer p-3"
      onclick="window.closeModal()">
      &times;
    </button>
    <div class="flex flex-col items-center max-w-5xl relative">
      <!-- Кнопка "Предыдущее изображение" -->
      <button id="prevImageBtn"
        class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-12 md:-translate-x-16 text-white text-4xl font-bold hover:text-accent-DEFAULT transition-colors z-10 cursor-pointer"
        onclick="window.navigateGallery(-1)" type="button">
        <i class="fas fa-chevron-left"></i>
      </button>

      <!-- Контейнер для изображения с поддержкой зума -->
      <div id="imageContainer" class="relative overflow-hidden cursor-zoom-in">
        <img id="modalImage"
             class="max-w-full max-h-[80vh] object-contain transition-opacity duration-300 ease-in-out"
             src="" alt=""
             onclick="window.toggleImageZoom(event)"
             onmousemove="window.moveZoomedImage(event)"
             style="touch-action: none;"
             />
      </div>

      <div id="modalCaption" class="text-white text-center p-4 text-lg"></div>

      <!-- Подсказки о функциях галереи -->
      <div class="text-white text-center text-sm opacity-70 mb-2 flex flex-wrap justify-center gap-x-4 gap-y-1 gallery-hint">
        <span class="inline-flex items-center">
          <i class="fas fa-search-plus mr-1"></i> Нажмите на изображение для увеличения
        </span>
        <span class="inline-flex items-center">
          <i class="fas fa-keyboard mr-1"></i> Используйте стрелки ← → для навигации
        </span>
      </div>

      <!-- Дополнительная кнопка закрытия внизу для мобильных устройств -->
      <button id="closeGalleryBtnBottom" type="button" class="mt-4 py-2 px-6 bg-gray-800 text-white rounded-full hover:bg-gray-700 transition-colors md:hidden">
        Закрыть
      </button>

      <!-- Индикатор положения в галерее -->
      <div id="galleryIndicator" class="flex justify-center space-x-2 mt-2"></div>

      <!-- Кнопка "Следующее изображение" -->
      <button id="nextImageBtn"
        class="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-12 md:translate-x-16 text-white text-4xl font-bold hover:text-accent-DEFAULT transition-colors z-10 cursor-pointer"
        onclick="window.navigateGallery(1)" type="button">
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
  </div>

  <!-- Pricing Section -->
  <section class="section bg-white dark:bg-dark transition-colors duration-300" id="pricing">
    <div class="container">
      <div class="section-title" data-aos="fade-up">
        <h2>Гибкие условия использования</h2>
        <p>Выберите подходящий вариант для вашего бизнеса</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto">
        <!-- Базовый тариф -->
        <div class="pricing-card flex flex-col" data-aos="fade-up" data-aos-delay="100">
          <div>
            <h3>Попробовать Бесплатно</h3>
            <div class="price">0₽ <span>/месяц</span></div>
            <div class="price-note">Бесплатно со всеми функциями</div>
            <div class="price-info">После истечения 30 дней все функции продолжают работать, кроме истории чатов</div>
            <ul class="pricing-features">
              <li>Неограниченные чаты</li>
              <li>Базовые функции поддержки</li>
              <li>Архив чатов</li>
              <li>Все чаты сохранятся даже после удаления</li>
              <li>Резервное копирование</li>
            </ul>
          </div>
          <div class="mt-auto pt-6">
            <a href="#" class="btn pricing-btn">Начать бесплатно</a>
          </div>
        </div>

        <!-- Премиум тариф с вариантами оплаты -->
        <div class="pricing-card flex flex-col" data-aos="fade-up" data-aos-delay="200">
          <div>
            <h3>Рассчитать</h3>

            <!-- Варианты оплаты -->
            <div class="subscription-options mb-6">
              <!-- Переключатель периодов подписки -->
              <div class="subscription-period-selector mb-6">
                <div class="text-center mb-3">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Выберите период подписки</span>
                </div>
                <div class="period-buttons-container">
                  <div class="period-buttons-wrapper">
                    <button type="button" class="period-btn active" data-period="1" data-days="30">1 месяц</button>
                    <button type="button" class="period-btn" data-period="3" data-days="90">3 месяца</button>
                    <button type="button" class="period-btn" data-period="6" data-days="180">6 месяцев</button>
                    <div class="popular-period-container">
                      <span class="popular-label">Популярный</span>
                      <button type="button" class="period-btn" data-period="12" data-days="365">1 год</button>
                    </div>
                    <button type="button" class="period-btn" data-period="24" data-days="730">2 года</button>
                  </div>
                </div>
              </div>

              <!-- Варианты размещения -->
              <div class="hosting-options mb-4">
                <!-- Период 1 месяц -->
                <div class="period-option active" data-period="1">
                  <div class="payment-option active" data-price="399" data-period="own">
                    <div class="price">399₽ <span>/месяц</span></div>
                    <div class="price-info-secondary">Установка на ваш сервер</div>
                    <div class="price-total">Итого: 399₽ за 30 дней</div>
                  </div>
                  <div class="payment-option" data-price="499" data-period="rent">
                    <div class="price">499₽ <span>/месяц</span></div>
                    <div class="price-info-secondary">Включая аренду нашего сервера</div>
                    <div class="price-total">Итого: 499₽ за 30 дней</div>
                  </div>
                </div>

                <!-- Период 3 месяца -->
                <div class="period-option" data-period="3">
                  <div class="payment-option active" data-price="1099" data-period="own">
                    <div class="price">1099₽ <span>/3 мес.</span></div>
                    <div class="price-saving">Экономия 98₽</div>
                    <div class="price-info-secondary">Установка на ваш сервер</div>
                    <div class="price-total">Итого: 1099₽ за 90 дней</div>
                  </div>
                  <div class="payment-option" data-price="1399" data-period="rent">
                    <div class="price">1399₽ <span>/3 мес.</span></div>
                    <div class="price-saving">Экономия 98₽</div>
                    <div class="price-info-secondary">Включая аренду нашего сервера</div>
                    <div class="price-total">Итого: 1399₽ за 90 дней</div>
                  </div>
                </div>

                <!-- Период 6 месяцев -->
                <div class="period-option" data-period="6">
                  <div class="payment-option active" data-price="1999" data-period="own">
                    <div class="price">1999₽ <span>/6 мес.</span></div>
                    <div class="price-saving">Экономия 395₽</div>
                    <div class="price-info-secondary">Установка на ваш сервер</div>
                    <div class="price-total">Итого: 1999₽ за 180 дней</div>
                  </div>
                  <div class="payment-option" data-price="2499" data-period="rent">
                    <div class="price">2499₽ <span>/6 мес.</span></div>
                    <div class="price-saving">Экономия 495₽</div>
                    <div class="price-info-secondary">Включая аренду нашего сервера</div>
                    <div class="price-total">Итого: 2499₽ за 180 дней</div>
                  </div>
                </div>

                <!-- Период 1 год -->
                <div class="period-option" data-period="12">
                  <div class="payment-option active" data-price="3800" data-period="own">
                    <div class="price">3800₽ <span>/год</span></div>
                    <div class="price-saving">Экономия 988₽</div>
                    <div class="price-info-secondary">Установка на ваш сервер</div>
                    <div class="price-total">Итого: 3800₽ за 365 дней</div>
                  </div>
                  <div class="payment-option" data-price="4800" data-period="rent">
                    <div class="price">4800₽ <span>/год</span></div>
                    <div class="price-saving">Экономия 1188₽</div>
                    <div class="price-info-secondary">Включая аренду нашего сервера</div>
                    <div class="price-total">Итого: 4800₽ за 365 дней</div>
                  </div>
                </div>

                <!-- Период 2 года -->
                <div class="period-option" data-period="24">
                  <div class="payment-option active" data-price="6999" data-period="own">
                    <div class="price">6999₽ <span>/2 года</span></div>
                    <div class="price-saving">Экономия 2577₽</div>
                    <div class="price-info-secondary">Установка на ваш сервер</div>
                    <div class="price-total">Итого: 6999₽ за 730 дней</div>
                  </div>
                  <div class="payment-option" data-price="8999" data-period="rent">
                    <div class="price">8999₽ <span>/2 года</span></div>
                    <div class="price-saving">Экономия 2977₽</div>
                    <div class="price-info-secondary">Включая аренду нашего сервера</div>
                    <div class="price-total">Итого: 8999₽ за 730 дней</div>
                  </div>
                </div>
              </div>

              <!-- Переключатель вариантов размещения -->
              <div class="payment-toggle">
                <span class="payment-toggle-label active">Ваш сервер</span>
                <label class="payment-toggle-switch">
                  <input type="checkbox" id="paymentToggle">
                  <span class="payment-toggle-slider"></span>
                </label>
                <span class="payment-toggle-label">Наш сервер</span>
              </div>
            </div>

            <ul class="pricing-features">
              <li>Неограниченные чаты</li>
              <li>Неограниченные рефералы</li>
              <li>Неограниченный архив чатов</li>
              <li>Расширенное резервное копирование</li>
              <li>Расширенная аналитика</li>
              <li>Продвинутые шаблоны ответов</li>
              <li>Экспорт истории в файл</li>
              <li>Рейтинг ответов</li>
              <li>Статистика оператора</li>
              <li>Персональный менеджер</li>
              <li>VIP поддержка 24/7</li>
            </ul>
          </div>
          <div class="mt-auto pt-6">
            <a href="#" class="btn pricing-btn">Выбрать полную версию</a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section
    class="py-20 bg-gradient-to-r from-primary to-secondary dark:from-primary-dark dark:to-blue-800 text-white text-center">
    <div class="container" data-aos="fade-up">
      <h2 class="text-3xl md:text-4xl font-bold mb-6">
        Готовы улучшить вашу поддержку клиентов?
      </h2>
      <p class="max-w-2xl mx-auto mb-10 opacity-90">
        Начните бесплатный 30-дневный пробный период прямо сейчас!
      </p>
      <a href="https://t.me/rev_support_bot?start=bot_revive" class="btn bg-white text-primary hover:bg-light hover:text-secondary dark:bg-white dark:text-primary-dark dark:hover:bg-gray-100">Попробовать бесплатно</a>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-secondary dark:bg-secondary-dark text-white py-16 transition-colors duration-300" id="contact">
    <div class="container">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-10 mb-8 md:mb-10">
        <div data-aos="fade-up" data-aos-delay="100">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Telegram Support Bot
          </h3>
          <p class="mb-6">
            Профессиональное решение для поддержки клиентов в Telegram с
            удобным интерфейсом для операторов и пользователей.
          </p>

        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Меню
          </h3>
          <ul class="space-y-3">
            <li>
              <a href="#why-us" class="text-gray-300 hover:text-white transition-colors">Почему Мы</a>
            </li>
            <li>
              <a href="#features" class="text-gray-300 hover:text-white transition-colors">Возможности</a>
            </li>
            <li>
              <a href="#screenshots" class="text-gray-300 hover:text-white transition-colors">Скриншоты</a>
            </li>
            <li>
              <a href="#pricing" class="text-gray-300 hover:text-white transition-colors">Тарифы</a>
            </li>
            <li>
              <a href="docs.html" class="text-gray-300 hover:text-white transition-colors">Документация</a>
            </li>
          </ul>
        </div>

        <div data-aos="fade-up" data-aos-delay="300">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Поддержка
          </h3>
          <ul class="space-y-3">
            <li>
              <a href="docs.html#faq" class="text-gray-300 hover:text-white transition-colors">FAQ</a>
            </li>
            <li>
              <a href="javascript:void(0)" class="text-gray-300 hover:text-white transition-colors">Контакты</a>
            </li>
            <li>
              <a href="privacy.html" class="text-gray-300 hover:text-white transition-colors">Политика
                конфиденциальности</a>
            </li>
            <li>
              <a href="terms.html" class="text-gray-300 hover:text-white transition-colors">Условия
                использования</a>
            </li>
          </ul>
        </div>

        <div data-aos="fade-up" data-aos-delay="400">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Контакты
          </h3>
          <ul class="space-y-3">
            <li class="flex items-center space-x-2">
              <i class="fas fa-envelope"></i>
              <a href="mailto:<EMAIL>"
                class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
            </li>
            <li class="flex items-center space-x-2">
              <i class="fab fa-telegram"></i>
              <a href="https://t.me/rev_support_bot?start=bot_revive" target="_blank" class="text-gray-300 hover:text-white transition-colors">Наш бот в Telegram</a>
            </li>
          </ul>
        </div>
      </div>

      <div class="text-center pt-8 border-t border-gray-800">
        <div class="flex items-center justify-center mb-4">
          <p class="text-gray-300 text-base sm:text-base text-sm flex flex-wrap sm:flex-nowrap items-center justify-center">
            Сделано с <span class="text-red-500 mx-1 text-lg sm:text-lg text-base">❤️</span> в
            <a href="https://revive-it.ru" target="_blank" class="flex items-center ml-2">
              <img src="assets/img/revive-it-logo.svg" class="img-responsive sm:h-[30px] h-[24px]" alt="Revive-IT" style="width: auto; vertical-align: middle;">
            </a>
          </p>
        </div>
        <p class="text-gray-400 text-sm">
          &copy; 2025 Telegram Support Bot. Все права защищены.
        </p>
        <p class="text-gray-500 text-xs mt-2">
          Версия: <span id="app-version">1.0.1</span>
        </p>
      </div>
    </div>
  </footer>

  <!-- Модальное окно согласия с политикой конфиденциальности -->
  <div id="privacyConsentModal"
    class="fixed inset-0 z-[60] hidden bg-black bg-opacity-90 flex flex-col justify-center items-center p-4">
    <div class="bg-white dark:bg-dark-dark rounded-xl max-w-lg w-full p-6 relative shadow-xl">
      <div class="text-center mb-6">
        <h3 class="text-2xl font-bold text-secondary dark:text-light mb-4">Согласие на обработку персональных данных</h3>
        <p class="text-gray-600 dark:text-gray-300 text-left mb-4">
          Для улучшения качества наших услуг и предоставления вам актуальной информации о продукте, мы просим ваше согласие на обработку персональных данных.
        </p>
      </div>

      <div class="mb-6">
        <label class="flex items-start space-x-3 cursor-pointer">
          <input type="checkbox" id="privacyConsentCheckbox"
            class="mt-1 h-4 w-4 text-primary dark:text-primary-dark border-gray-300 dark:border-gray-600 rounded focus:ring-primary dark:focus:ring-primary-dark focus:ring-2">
          <span class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            Согласен(на) на получение информационных материалов от сайта по email или мессенджеру Telegram в соответствии с
            <a href="privacy.html" target="_blank" class="text-primary dark:text-primary-dark hover:underline">Политикой конфиденциальности</a>
          </span>
        </label>
      </div>

      <div class="flex flex-col sm:flex-row gap-3">
        <button id="privacyConsentAccept"
          class="flex-1 bg-primary dark:bg-primary-dark text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-dark dark:hover:bg-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled>
          Согласиться
        </button>
        <button id="privacyConsentDecline"
          class="flex-1 bg-gray-500 dark:bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-600 dark:hover:bg-gray-700 transition-colors duration-200">
          Отказаться
        </button>
      </div>

      <p class="text-xs text-gray-500 dark:text-gray-400 mt-4 text-center">
        При отказе от согласия функциональность сайта не ограничивается
      </p>
    </div>
  </div>

  <!-- Модальное окно для демо-версии -->
  <div id="demoModal"
    class="fixed inset-0 z-50 hidden bg-black bg-opacity-80 flex flex-col justify-center items-center p-4">
    <div class="bg-white dark:bg-dark-dark rounded-xl max-w-md w-full p-6 relative shadow-xl">
      <button class="absolute top-3 right-3 text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-dark text-2xl font-bold"
        onclick="closeDemoModal()">
        &times;
      </button>
      <div class="text-center mb-6">
        <h3 class="text-2xl font-bold text-secondary dark:text-light mb-2">Попробовать демо-версию</h3>
        <p class="text-gray-600 dark:text-gray-300">Вы можете протестировать интерфейс Пользователя или перейти к самостоятельной установке. После которой вы сами назначите Оператора.</p>
      </div>
      <div class="flex flex-col space-y-4 mb-6">
        <a href="https://t.me/rev_support_bot?start=bot_revive" target="_blank"
          class="btn flex items-center justify-center w-full" onclick="closeDemoModal()">
          <i class="fab fa-telegram mr-2"></i> Интерфейс пользователя
        </a>
        <a href="docs.html#downloads"
          class="btn btn-secondary flex items-center justify-center w-full" onclick="closeDemoModal()">
          <i class="fas fa-download mr-2"></i> Перейти к самостоятельной установке
        </a>
      </div>
      <div class="text-center">
        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">Нужна помощь с настройкой?</p>
        <p class="text-gray-700 dark:text-gray-200">Свяжитесь с нашим оператором через интерфейс пользователя, и мы поможем вам настроить систему под ваши потребности.</p>
      </div>
    </div>
  </div>

  <!-- Модальное окно для выбора тарифа -->
  <div id="pricingModal"
    class="fixed inset-0 z-50 hidden bg-black bg-opacity-80 flex flex-col justify-center items-center p-4">
    <div class="bg-white dark:bg-dark-dark rounded-xl max-w-md w-full p-6 relative shadow-xl">
      <button class="absolute top-3 right-3 text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-dark text-2xl font-bold"
        onclick="closePricingModal()">
        &times;
      </button>
      <div class="text-center mb-6">
        <h3 class="text-2xl font-bold text-secondary dark:text-light mb-2">Выбрать полную версию</h3>
        <p class="text-gray-600 dark:text-gray-300">Первый месяц использования полной версии бесплатно!</p>
      </div>
      <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg mb-6">
        <p class="text-green-600 dark:text-green-400 font-medium">Никаких платежей сейчас не требуется. Оплата начнется только через 30 дней.</p>
      </div>
      <div class="flex justify-center mb-6">
        <button type="button" id="connectFullVersionBtn"
          class="btn flex items-center justify-center w-full">
          <i class="fab fa-telegram mr-2"></i> Подключить полную версию
        </button>
      </div>
      <div class="text-center">
        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">У вас есть вопросы?</p>
        <p class="text-gray-700 dark:text-gray-200">Свяжитесь с нашим менеджером прямо через бота, и мы поможем вам с настройкой и ответим на все вопросы.</p>
      </div>
    </div>
  </div>

  <!-- Модальное окно для уведомления об обновлении сайта -->
  <div id="updateModal"
    class="fixed bottom-4 right-4 z-50 hidden transform transition-all duration-300 ease-in-out scale-95 opacity-0">
    <div class="bg-white dark:bg-dark-dark rounded-lg shadow-xl overflow-hidden max-w-sm w-full border border-blue-100 dark:border-blue-900">
      <div class="bg-blue-50 dark:bg-blue-900/30 px-4 py-2 flex justify-between items-center">
        <div class="flex items-center">
          <i class="fas fa-sync-alt text-blue-500 dark:text-blue-400 mr-2 animate-spin-slow"></i>
          <h3 class="font-medium text-blue-700 dark:text-blue-300">Доступно обновление</h3>
        </div>
        <button id="closeUpdateBtn" type="button" onclick="window.closeUpdateModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors cursor-pointer">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="p-4">
        <p class="text-gray-600 dark:text-gray-300 mb-4">Доступна новая версия сайта. Обновить сейчас, чтобы получить доступ к новым функциям и улучшениям.</p>
        <div class="flex space-x-2">
          <button id="updateNowBtn" type="button" onclick="window.updateNow()" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center cursor-pointer">
            <i class="fas fa-sync-alt mr-2"></i> Обновить сейчас
          </button>
          <button id="updateLaterBtn" type="button" onclick="window.closeUpdateModal()" class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-md transition-colors duration-200 cursor-pointer">
            Позже
          </button>
        </div>
      </div>
    </div>
  </div>
</body>

</html>
