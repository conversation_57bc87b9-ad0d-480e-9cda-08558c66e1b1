# 🚀 Быстрое развертывание на хостинге

## ⚡ Для тех, кто спешит

### 1. Соберите проект:
```bash
npm run build
```

### 2. Загрузите на хостинг:
Загрузите **ВСЕ** содержимое папки `dist/` в корневую директорию вашего сайта.

### 3. Готово! 🎉
Ваш сайт с системой согласий работает!

---

## 🔧 Административная панель

### Доступ к данным согласий:
1. Откройте любую страницу сайта
2. Нажмите **F12** (консоль браузера)
3. Нажмите **Ctrl+Shift+C** или введите `ConsentAdmin.help()`

### Основные команды:
```javascript
ConsentAdmin.showStats()      // Статистика согласий
ConsentAdmin.exportCSV()      // Скачать CSV для Excel
ConsentAdmin.exportLogs()     // Скачать JSON
```

---

## 📋 Что включено

- ✅ **Модальное окно согласия** при первом посещении
- ✅ **Локальное логирование** (IP, дата, источник)
- ✅ **Экспорт данных** в JSON и CSV
- ✅ **Административная панель** в консоли браузера
- ✅ **Соответствие GDPR** и российскому законодательству

---

## 📖 Подробная документация

- **[README.md](README.md)** - Полное описание проекта
- **[docs/QUICK_START.md](docs/QUICK_START.md)** - Детальный быстрый старт
- **[docs/HOSTING_GUIDE.md](docs/HOSTING_GUIDE.md)** - Руководство по хостингу
- **[docs/CONSENT_DOCUMENTATION.md](docs/CONSENT_DOCUMENTATION.md)** - Техническая документация

---

## 🧪 Тестирование

Откройте `test-consent.html` в браузере для тестирования всех функций системы согласий.

---

**Система готова к продакшену! 🎉**
