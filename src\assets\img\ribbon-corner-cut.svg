<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="ribbonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ff3333" stop-opacity="1"/>
      <stop offset="100%" stop-color="#cc0000" stop-opacity="1"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Основной треугольник -->
  <path d="M 0,0 L 100,0 L 100,100 z" fill="url(#ribbonGradient)" filter="url(#shadow)"/>

  <!-- Тень для создания эффекта складки -->
  <path d="M 0,0 L 100,0 L 100,15 z" fill="#990000" opacity="0.2"/>

  <!-- Текст, наклоненный по диагонали и расположенный по центру грани -->
  <text x="64" y="39" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white" text-anchor="middle" transform="rotate(45, 64, 39)" filter="url(#shadow)" letter-spacing="0.5">Скоро</text>
</svg>
