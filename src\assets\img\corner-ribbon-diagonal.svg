<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="ribbonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ff3333" stop-opacity="1"/>
      <stop offset="100%" stop-color="#cc0000" stop-opacity="1"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Основной треугольник -->
  <path d="M 0,0 L 120,0 L 120,120 z" fill="url(#ribbonGradient)" filter="url(#shadow)"/>
  
  <!-- Тень для создания эффекта складки -->
  <path d="M 0,0 L 120,0 L 120,20 z" fill="#990000" opacity="0.2"/>
  
  <!-- Текст, наклоненный по диагонали -->
  <text x="60" y="35" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white" text-anchor="middle" transform="rotate(45, 60, 35)" filter="url(#shadow)">Скоро</text>
</svg>
