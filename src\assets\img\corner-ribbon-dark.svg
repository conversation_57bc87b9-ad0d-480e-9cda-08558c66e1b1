<?xml version="1.0" encoding="UTF-8"?>
<svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="ribbonGradientDark" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4f8df9" stop-opacity="1"/>
      <stop offset="100%" stop-color="#2563eb" stop-opacity="1"/>
    </linearGradient>
    <filter id="shadowDark" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000" flood-opacity="0.5"/>
    </filter>
  </defs>
  
  <!-- Основная лента -->
  <g transform="rotate(-45, 75, 75)">
    <!-- Основная часть ленты -->
    <path d="M 0,40 L 150,40 L 150,70 L 0,70 Z" fill="url(#ribbonGradientDark)" filter="url(#shadowDark)"/>
    
    <!-- Загнутые края -->
    <path d="M 0,40 L 0,70 L 10,75 L 10,35 Z" fill="#1e40af"/>
    <path d="M 150,40 L 150,70 L 140,75 L 140,35 Z" fill="#1e40af"/>
    
    <!-- Текст -->
    <text x="75" y="60" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle" filter="url(#shadowDark)">Скоро</text>
  </g>
</svg>
