<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="ribbonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0088ff" stop-opacity="1"/>
      <stop offset="100%" stop-color="#0055dd" stop-opacity="1"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  <!-- Основная лента -->
  <path d="M 0,0 L 120,0 L 120,30 L 110,40 L 120,50 L 120,120 L 0,0 Z" fill="url(#ribbonGradient)" filter="url(#shadow)"/>
  
  <!-- Тень для создания эффекта складки -->
  <path d="M 110,40 L 120,30 L 120,50 Z" fill="#00448c" opacity="0.5"/>
  
  <!-- Текст -->
  <text x="60" y="25" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle" transform="rotate(45, 60, 25)" filter="url(#shadow)">Скоро</text>
</svg>
