# 🚀 Локальная система согласий с политикой конфиденциальности

## ✅ Что реализовано

### 1. Модальное окно согласия
- ✅ **Умная логика показа**:
  - На обычных страницах: через 1 секунду после загрузки
  - На странице политики конфиденциальности: после прокрутки до конца
- ✅ Галочка для согласия (обязательная для активации кнопки)
- ✅ Текст: "Согласен(на) на получение информационных материалов от сайта по email или мессенджеру Telegram"
- ✅ Две кнопки: "Согласиться" и "Отказаться"
- ✅ Ссылка на политику конфиденциальности
- ✅ Адаптивный дизайн + темная тема
- ✅ Работает на всех страницах сайта

### 2. Локальное логирование данных
При согласии/отказе фиксируется:
- ✅ **IP адрес** пользователя (через ipify.org API)
- ✅ **Дата и время** решения
- ✅ **Источник** перехода (referrer)
- ✅ **Страница** на которой было принято решение
- ✅ **User Agent** браузера
- ✅ **Решение** (согласие/отказ)
- ✅ **Уникальный ID** записи

### 3. Административная панель
- ✅ Экспорт логов в JSON формат
- ✅ Экспорт логов в CSV для Excel
- ✅ Статистика согласий
- ✅ Просмотр последних записей
- ✅ Очистка логов
- ✅ Горячие клавиши (Ctrl+Shift+C)

### 4. Файлы системы
- ✅ `src/index.js` - основная логика и административная панель
- ✅ `src/index.html` - модальное окно на главной
- ✅ `src/docs.html` - модальное окно на документации
- ✅ `src/terms.html` - модальное окно на условиях
- ✅ `src/privacy.html` - модальное окно на политике
- ✅ `test-consent.html` - тестовая страница с полным функционалом

## 🧪 Тестирование

### 1. Тест модального окна
Откройте файл `test-consent.html` в браузере:
```bash
# Откройте в браузере
open test-consent.html
# или
start test-consent.html
```

**Функции тестовой страницы:**
- ✅ Показать модальное окно согласия
- ✅ Очистить сохраненное согласие
- ✅ Экспорт логов в JSON
- ✅ Экспорт логов в CSV для Excel
- ✅ Показать статистику согласий
- ✅ Переключить тему (светлая/темная)
- ✅ Просмотр статуса согласия

### 2. Тест на реальном сайте

#### Локальное тестирование:
```bash
# 1. Соберите проект
npm run build

# 2. Запустите локальный сервер (если доступен)
npm run serve
# или откройте dist/index.html в браузере
```

#### На хостинге:
```bash
# 1. Соберите проект
npm run build

# 2. Загрузите содержимое папки dist/ на хостинг
# 3. Откройте ваш сайт в браузере
```

**Подробная инструкция по размещению на хостинге:** см. файл `HOSTING_GUIDE.md`

### 3. Административная панель
Откройте консоль браузера (F12) и используйте команды:
```javascript
// Показать справку
ConsentAdmin.help()

// Экспорт логов
ConsentAdmin.exportLogs()
ConsentAdmin.exportCSV()

// Статистика
ConsentAdmin.showStats()

// Последние записи
ConsentAdmin.showRecentLogs(10)
```

**Горячие клавиши:**
- `Ctrl+Shift+C` - показать справку административной панели

## 📋 Как работает система

### При первом посещении:
1. JavaScript проверяет localStorage на наличие согласия
2. Если согласие не дано - определяется логика показа:
   - **Обычные страницы**: модальное окно через 1 секунду
   - **Страница privacy.html**: модальное окно после прокрутки до конца
3. Пользователь ставит галочку и выбирает действие
4. Данные сохраняются в localStorage браузера
5. Создается запись в локальном логе согласий
6. Модальное окно больше не показывается этому пользователю

### Хранение данных:
- **Согласие пользователя**: `localStorage['privacy_consent_given']`
- **Данные согласия**: `localStorage['privacy_consent_data']`
- **Лог всех согласий**: `localStorage['consent_logs']` (массив записей)

### Структура записи в логе:
```json
{
  "id": "1704067200000abc123",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "consent": true,
  "ip": "***********",
  "date": "2024-01-01T00:00:00.000Z",
  "source": "https://google.com",
  "userAgent": "Mozilla/5.0...",
  "page": "https://bot.revive-it.ru/"
}
```

### Ограничения:
- Максимум 1000 записей в логе (автоматическая очистка старых)
- Данные хранятся только в браузере пользователя
- При очистке данных браузера логи удаляются

## 🔧 Настройка

### Изменить задержку показа модального окна:
В `src/index.js` найдите:
```javascript
setTimeout(() => {
  this.showConsentModal();
}, 1000); // Измените на нужное значение в миллисекундах
```

### Изменить текст согласия:
В HTML файлах найдите блок с `privacyConsentModal` и измените текст в элементе:
```html
<span class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
  Согласен(на) на получение информационных материалов...
</span>
```

### Изменить лимит записей в логе:
В `src/index.js` найдите:
```javascript
if (existingLogs.length > 1000) { // Измените лимит
  existingLogs.splice(0, existingLogs.length - 1000);
}
```

### Отключить получение IP адреса:
В `src/index.js` в функции `getUserIP` замените на:
```javascript
getUserIP: async function() {
  return 'disabled'; // Отключает получение IP
}
```

## 📊 Мониторинг и экспорт данных

### Административная панель (консоль браузера):
```javascript
// Показать статистику
ConsentAdmin.showStats()

// Экспорт в JSON
ConsentAdmin.exportLogs()

// Экспорт в CSV для Excel
ConsentAdmin.exportCSV()

// Последние 20 записей
ConsentAdmin.showRecentLogs(20)

// Справка по командам
ConsentAdmin.help()
```

### Пример статистики:
```
📊 Статистика согласий:

Всего записей: 150
Согласились: 120
Отказались: 30
Сегодня: 15
За неделю: 45
За месяц: 150
Уникальных IP: 142
```

### Форматы экспорта:
- **JSON**: полные данные для программной обработки
- **CSV**: для анализа в Excel с русскими заголовками
- **Консоль**: таблицы для быстрого просмотра

## 🛠️ Устранение неполадок

### Модальное окно не показывается:
1. Проверьте консоль браузера на ошибки (F12)
2. Очистите localStorage: `localStorage.clear()`
3. Обновите страницу (F5)
4. Проверьте, что согласие не было дано ранее

### Логирование не работает:
1. Проверьте консоль браузера на ошибки
2. Проверьте доступ к интернету (для получения IP через ipify.org)
3. Убедитесь, что localStorage доступен в браузере

### Экспорт не работает:
1. Проверьте, что браузер поддерживает File API
2. Убедитесь, что есть данные для экспорта: `ConsentAdmin.showStats()`
3. Проверьте настройки браузера для скачивания файлов

### Данные пропали:
1. Данные хранятся в localStorage браузера
2. При очистке данных браузера логи удаляются
3. Регулярно экспортируйте данные для резервного копирования

### Проверка работоспособности:
```javascript
// В консоли браузера
console.log('Согласие дано:', localStorage.getItem('privacy_consent_given'));
console.log('Количество логов:', JSON.parse(localStorage.getItem('consent_logs') || '[]').length);
```

## 💾 Хранение и резервное копирование

### Где хранятся данные:
- **localStorage браузера**: все данные согласий
- **Экспортированные файлы**: JSON и CSV файлы для резервного копирования

### Резервное копирование:
```javascript
// Регулярно экспортируйте данные
ConsentAdmin.exportLogs() // JSON формат
ConsentAdmin.exportCSV()  // CSV для Excel
```

### Восстановление данных:
Данные можно восстановить только из экспортированных файлов, так как они хранятся локально в браузере.

## 🔒 Безопасность и конфиденциальность

### Преимущества локального хранения:
- ✅ Данные не передаются на внешние серверы
- ✅ Полный контроль над данными
- ✅ Соответствие требованиям GDPR
- ✅ Нет зависимости от внешних сервисов

### Ограничения:
- ⚠️ Данные привязаны к браузеру пользователя
- ⚠️ При очистке браузера данные удаляются
- ⚠️ Нет централизованного хранения

### IP адреса:
- Получаются через внешний сервис ipify.org
- Можно отключить (см. раздел настройки)
- Используются только для логирования

## 🎯 Соответствие требованиям

Система полностью соответствует всем требованиям:
- ✅ Модальное окно при первом входе на сайт
- ✅ Галочка для согласия (обязательная)
- ✅ Текст о получении информационных материалов
- ✅ Кнопки "Согласиться" и "Отказаться"
- ✅ Логирование IP, даты, источника
- ✅ Сохранение в отдельный файл (через экспорт)
- ✅ Административная панель для управления

## 📈 Преимущества решения

### Простота:
- Нет необходимости в отдельном сервере
- Работает на любом хостинге
- Минимальные требования к инфраструктуре

### Надежность:
- Нет зависимости от внешних сервисов
- Работает офлайн
- Автоматическое ограничение размера логов

### Удобство:
- Административная панель в консоли браузера
- Экспорт в популярные форматы
- Горячие клавиши для быстрого доступа
