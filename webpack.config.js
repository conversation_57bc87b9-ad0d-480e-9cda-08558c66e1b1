import path from 'path';
import { fileURLToPath } from 'url';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import CopyPlugin from 'copy-webpack-plugin';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Конфигурация Webpack для сборки проекта
 * Настройки для сборки и оптимизации кода
 */
export default {
  // Режим сборки (разработка или продакшн)
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',

  // Точка входа в приложение
  entry: './src/index.js',

  // Настройки выходных файлов
  output: {
    filename: 'bundle.[contenthash].js',
    path: path.resolve(__dirname, 'dist'),
    clean: true, // Очищать папку dist перед сборкой
  },

  // Оптимизация для продакшн-сборки
  optimization: {
    minimize: process.env.NODE_ENV === 'production', // Минимизировать только в продакшне
    minimizer: [
      // Настройки минимизаторов для JS и CSS
      '...', // Использовать стандартные минимизаторы
    ],
  },
  devServer: {
    static: {
      directory: path.resolve(__dirname, 'dist'),
    },
    port: 3000,
    open: false, // Не открываем браузер автоматически, чтобы избежать открытия нескольких вкладок
    client: {
      overlay: true,
      progress: true,
      logging: 'info',
      // Явно запрещаем автоматическое открытие браузера
      webSocketURL: {
        hostname: 'localhost',
        pathname: '/ws',
        port: 3000,
      }
    },
    hot: true,
    compress: true,
    historyApiFallback: {
      rewrites: [
        { from: /^\/docs$/, to: '/docs.html' },
        { from: /^\/terms$/, to: '/terms.html' },
        { from: /./, to: '/index.html' }
      ]
    },
  },
  /**
   * Настройки модулей и обработчиков файлов
   */
  module: {
    rules: [
      // Обработка CSS файлов
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              // Удалять комментарии в продакшн-режиме
              sourceMap: process.env.NODE_ENV !== 'production',
            }
          },
          'postcss-loader', // Используем настройки из postcss.config.js
        ],
      },
      // Обработка JavaScript файлов
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', { targets: "defaults" }]
            ],
            // Удаляем комментарии в продакшн-режиме
            comments: process.env.NODE_ENV !== 'production',
          }
        }
      },
      // Обработка изображений
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset/resource',
        generator: {
          // Добавляем хеш к именам файлов для предотвращения кэширования
          filename: process.env.NODE_ENV === 'production'
            ? 'assets/img/[name].[contenthash][ext]'
            : 'assets/img/[name][ext]',
        },
      },
    ],
  },
  /**
   * Плагины для расширения функциональности Webpack
   */
  plugins: [
    // Генерация HTML файла из шаблона для главной страницы
    new HtmlWebpackPlugin({
      title: 'Telegram Support Bot - Профессиональная поддержка клиентов',
      template: './src/index.html',
      filename: 'index.html',
      // Минимизация HTML в продакшн-режиме
      minify: process.env.NODE_ENV === 'production' ? {
        collapseWhitespace: true,
        removeComments: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true
      } : false,
    }),

    // Генерация HTML файла для страницы документации
    new HtmlWebpackPlugin({
      title: 'Документация - Telegram Support Bot',
      template: './src/docs.html',
      filename: 'docs.html',
      // Минимизация HTML в продакшн-режиме
      minify: process.env.NODE_ENV === 'production' ? {
        collapseWhitespace: true,
        removeComments: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true
      } : false,
    }),

    // Генерация HTML файла для страницы условий использования
    new HtmlWebpackPlugin({
      title: 'Условия использования - Telegram Support Bot',
      template: './src/terms.html',
      filename: 'terms.html',
      // Минимизация HTML в продакшн-режиме
      minify: process.env.NODE_ENV === 'production' ? {
        collapseWhitespace: true,
        removeComments: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true
      } : false,
    }),

    // Генерация HTML файла для страницы политики конфиденциальности
    new HtmlWebpackPlugin({
      title: 'Политика конфиденциальности - Telegram Support Bot',
      template: './src/privacy.html',
      filename: 'privacy.html',
      // Минимизация HTML в продакшн-режиме
      minify: process.env.NODE_ENV === 'production' ? {
        collapseWhitespace: true,
        removeComments: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true
      } : false,
    }),

    // Извлечение CSS в отдельный файл
    new MiniCssExtractPlugin({
      filename: 'styles.[contenthash].css',
    }),

    // Копирование статических файлов
    new CopyPlugin({
      patterns: [
        { from: 'src/assets', to: 'assets' },
        { from: 'src/service-worker.js', to: 'service-worker.js' },
      ],
    }),
  ],
};
