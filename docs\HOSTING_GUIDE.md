# 🌐 Руководство по размещению на хостинге

## 📁 Файлы для загрузки

После выполнения команды `npm run build` все необходимые файлы находятся в папке `dist/`:

```
dist/
├── index.html              # Главная страница
├── docs.html               # Страница документации  
├── terms.html              # Условия использования
├── privacy.html            # Политика конфиденциальности
├── bundle.[hash].js        # JavaScript код (минифицированный)
├── styles.[hash].css       # CSS стили (минифицированный)
├── service-worker.js       # Service Worker для кэширования
└── assets/                 # Статические ресурсы
    ├── css/
    │   └── fonts.css       # Шрифты
    ├── fonts/              # Файлы шрифтов
    │   └── inter/
    └── img/                # Изображения
```

## 🚀 Инструкция по размещению

### 1. Подготовка файлов
```bash
# Соберите проект локально
npm run build

# Все готовые файлы будут в папке dist/
```

### 2. Загрузка на хостинг
Загрузите **ВСЕ** содержимое папки `dist/` в корневую директорию вашего сайта:

**Через FTP/SFTP:**
```
Локально: dist/index.html
На сервере: public_html/index.html (или www/index.html)

Локально: dist/assets/
На сервере: public_html/assets/ (или www/assets/)
```

**Через панель управления хостингом:**
1. Откройте файловый менеджер
2. Перейдите в корневую папку сайта
3. Загрузите все файлы из папки `dist/`

### 3. Проверка работоспособности
После загрузки проверьте:
- ✅ Главная страница: `https://ваш-домен.ru/`
- ✅ Документация: `https://ваш-домен.ru/docs.html`
- ✅ Политика: `https://ваш-домен.ru/privacy.html`
- ✅ Условия: `https://ваш-домен.ru/terms.html`

## 🔧 Система согласий на хостинге

### Как работает без сервера:
1. **Модальное окно** - работает полностью на клиенте
2. **Логирование** - данные сохраняются в localStorage браузера
3. **Экспорт данных** - через административную панель в консоли

### Доступ к административной панели:
1. Откройте любую страницу сайта
2. Нажмите F12 (консоль браузера)
3. Нажмите `Ctrl+Shift+C` или введите `ConsentAdmin.help()`

### Команды администратора:
```javascript
// Показать статистику
ConsentAdmin.showStats()

// Экспорт логов в JSON
ConsentAdmin.exportLogs()

// Экспорт в CSV для Excel
ConsentAdmin.exportCSV()

// Последние 10 записей
ConsentAdmin.showRecentLogs(10)

// Очистить все логи
ConsentAdmin.clearLogs()
```

## 📊 Сбор данных согласий

### Где хранятся данные:
- **localStorage браузера** - все согласия пользователей
- **Экспортированные файлы** - резервные копии для анализа

### Регулярный экспорт данных:
```javascript
// Рекомендуется выполнять еженедельно
ConsentAdmin.exportLogs()  // Скачает JSON файл
ConsentAdmin.exportCSV()   // Скачает CSV для Excel
```

### Анализ данных:
1. Откройте скачанный CSV файл в Excel
2. Данные уже отформатированы с русскими заголовками
3. Можно создавать сводные таблицы и графики

## 🛠️ Настройка хостинга

### Рекомендуемые настройки:

#### 1. HTTPS (обязательно)
```
Система согласий требует HTTPS для корректной работы
```

#### 2. Кэширование статических файлов
```apache
# .htaccess для Apache
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>
```

#### 3. Сжатие файлов
```apache
# .htaccess для Apache
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

#### 4. Service Worker (для кэширования)
```
Файл service-worker.js уже настроен и будет работать автоматически
```

## 🔄 Обновление сайта

### При внесении изменений:
1. Внесите изменения в исходный код
2. Выполните `npm run build`
3. Загрузите обновленные файлы из `dist/` на хостинг
4. Очистите кэш браузера (Ctrl+F5)

### Автоматическое обновление версии:
```bash
# Увеличить версию и собрать
npm run increment-version
npm run build
```

## 📱 Мобильная оптимизация

Сайт уже оптимизирован для мобильных устройств:
- ✅ Адаптивный дизайн
- ✅ Touch-friendly интерфейс
- ✅ Быстрая загрузка
- ✅ Оптимизированные изображения

## 🔍 SEO настройки

### Мета-теги уже настроены:
- Title и description для каждой страницы
- Open Graph теги
- Viewport для мобильных устройств
- Canonical URLs

### Дополнительные рекомендации:
1. Настройте robots.txt
2. Добавьте sitemap.xml
3. Подключите Google Analytics
4. Настройте Яндекс.Метрику

## 🛡️ Безопасность

### Рекомендуемые заголовки безопасности:
```apache
# .htaccess
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
```

## 📞 Поддержка

### При возникновении проблем:
1. Проверьте, что все файлы загружены
2. Убедитесь, что HTTPS настроен
3. Проверьте консоль браузера на ошибки
4. Очистите кэш браузера

### Проверка работоспособности:
```javascript
// В консоли браузера
console.log('Версия сайта:', document.getElementById('app-version')?.textContent);
console.log('Service Worker:', 'serviceWorker' in navigator);
console.log('localStorage:', typeof Storage !== 'undefined');
```

## 📈 Мониторинг

### Рекомендуемые инструменты:
- **Google Analytics** - для отслеживания посещений
- **Яндекс.Метрика** - для российской аудитории
- **Google Search Console** - для SEO мониторинга
- **PageSpeed Insights** - для проверки скорости

### Интеграция с аналитикой:
Система согласий автоматически отправляет события в Google Analytics и Яндекс.Метрику (если подключены).

## ✅ Чек-лист размещения

- [ ] Собран проект (`npm run build`)
- [ ] Загружены все файлы из `dist/`
- [ ] Настроен HTTPS
- [ ] Проверены все страницы
- [ ] Протестировано модальное окно согласий
- [ ] Настроено кэширование
- [ ] Добавлены мета-теги
- [ ] Подключена аналитика
- [ ] Проверена мобильная версия
