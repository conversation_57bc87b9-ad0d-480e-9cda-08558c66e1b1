<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="ribbonGradientDark" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#3b82f6" stop-opacity="1"/>
      <stop offset="100%" stop-color="#2563eb" stop-opacity="1"/>
    </linearGradient>
    <filter id="shadowDark" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.5"/>
    </filter>
  </defs>
  <g transform="rotate(45, 60, 60)">
    <rect x="10" y="50" width="140" height="30" fill="url(#ribbonGradientDark)" filter="url(#shadowDark)" rx="2" ry="2"/>
    <text x="80" y="70" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle" filter="url(#shadowDark)">Скоро</text>
  </g>
</svg>
