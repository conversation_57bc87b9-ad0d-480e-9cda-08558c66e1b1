<!DOCTYPE html>
<html lang="ru">

<head>
  <!-- Предотвращение мигания темы -->
  <script type="text/javascript">
    (function () {
      // Добавляем класс preload для предотвращения мигания
      document.documentElement.classList.add("preload");

      // Проверяем сохраненную тему или системные настройки
      var savedTheme = localStorage.getItem("theme");
      var prefersDark =
        window.matchMedia &&
        window.matchMedia("(prefers-color-scheme: dark)").matches;

      // Устанавливаем тему до загрузки основного контента
      if (savedTheme === "dark" || (!savedTheme && prefersDark)) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }

      // Удаляем класс preload после загрузки страницы
      window.addEventListener("load", function () {
        document.documentElement.classList.remove("preload");
      });
    })();
  </script>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- Мета-теги для управления кэшированием -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>Политика конфиденциальности - Telegram Support Bot</title>
  <!-- Стили -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
  <link rel="stylesheet" href="assets/css/fonts.css" />

  <!-- Скрипты -->
  <!-- Скрипты загружаются автоматически через webpack -->
</head>

<body class="bg-light dark:bg-dark-dark">
  <!-- Header -->
  <header class="bg-white dark:bg-dark shadow-md fixed w-full top-0 z-50 transition-colors duration-300">
    <div class="container">
      <nav class="flex justify-between items-center py-5">
        <div id="logo" class="mr-3">
          <a href="index.html" class="logo-link flex flex-col items-center">
            <img src="assets/img/revive-it-logo-secondary.svg" class="img-responsive mb-1 sm:h-[32px] h-[28px] block dark:hidden" alt="Revive-IT" style="width: auto;">
            <img src="assets/img/revive-it-logo.svg" class="img-responsive mb-1 sm:h-[32px] h-[28px] hidden dark:block" alt="Revive-IT" style="width: auto;">
            <span class="text-xs sm:text-sm font-medium text-primary dark:text-primary-dark">Telegram Support Bot</span>
          </a>
        </div>
        <ul class="hidden md:flex space-x-8">
          <li>
            <a href="index.html#why-us"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Почему Мы</a>
          </li>
          <li>
            <a href="index.html#features"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Возможности</a>
          </li>
          <li>
            <a href="index.html#screenshots"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Скриншоты</a>
          </li>
          <li>
            <a href="index.html#pricing"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Тарифы</a>
          </li>
          <li>
            <a href="docs.html"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Документация</a>
          </li>
        </ul>

        <!-- Переключатель темы -->
        <div class="flex items-center space-x-4">
          <button id="theme-toggle" class="theme-switch">
            <span class="sr-only">Переключить тему</span>
            <span class="theme-switch-track">
              <span class="theme-switch-thumb">
                <svg class="theme-switch-icon-sun" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                  fill="currentColor" width="16" height="16">
                  <path
                    d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                </svg>
                <svg class="theme-switch-icon-moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                  fill="currentColor" width="16" height="16">
                  <path fill-rule="evenodd"
                    d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"
                    clip-rule="evenodd" />
                </svg>
              </span>
            </span>
          </button>

          <button
            class="md:hidden cursor-pointer mobile-menu-button p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark">
            <span class="sr-only">Открыть меню</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-dark dark:text-light" fill="none"
              viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"
                class="menu-icon-bar" />
            </svg>
          </button>
        </div>
      </nav>
    </div>
  </header>

  <!-- Мобильное меню -->
  <div
    class="nav-links hidden md:hidden fixed top-[80px] left-0 w-full bg-white dark:bg-dark-dark shadow-lg z-40 transition-all duration-300 border-t border-gray-200 dark:border-gray-700 mobile-menu-container">
    <div class="container mx-auto pt-6 pb-4 px-4">
      <div class="grid gap-3">
        <a href="index.html#why-us" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
              clip-rule="evenodd" />
          </svg>
          Почему Мы
        </a>
        <a href="index.html#features" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
              clip-rule="evenodd" />
          </svg>
          Возможности
        </a>
        <a href="index.html#screenshots" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clip-rule="evenodd" />
          </svg>
          Скриншоты
        </a>
        <a href="index.html#pricing" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path
              d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
              clip-rule="evenodd" />
          </svg>
          Тарифы
        </a>
        <a href="docs.html" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
          </svg>
          Документация
        </a>
      </div>
    </div>
  </div>

  <!-- Основное содержимое страницы политики конфиденциальности -->
  <section class="pt-32 pb-24 bg-gradient-to-br from-light to-blue-50 dark:from-dark-dark dark:to-dark transition-colors duration-300">
    <div class="container" data-aos="fade-up">
      <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-secondary dark:text-light mb-4 md:mb-6 tracking-tight leading-tight text-center">
        Политика конфиденциальности
      </h1>
      <p class="text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mb-8 md:mb-10 leading-relaxed text-center">
        Политика в отношении обработки персональных данных
      </p>
    </div>
  </section>

  <!-- Секция политики конфиденциальности -->
  <section class="section bg-white dark:bg-dark-dark transition-colors duration-300">
    <div class="container">
      <div class="section-title" data-aos="fade-up">
        <h2>Политика в отношении обработки персональных данных</h2>
      </div>

      <div class="terms-content" data-aos="fade-up">
        <div class="bg-white dark:bg-dark-dark p-6 rounded-lg shadow-lg">
          <div class="mb-8">
            <h3 class="text-xl font-semibold text-secondary dark:text-light mb-4">1. Общие положения</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-4">
              Настоящая политика обработки персональных данных составлена в соответствии с требованиями Федерального закона от 27.07.2006. №152-ФЗ «О персональных данных» и определяет порядок обработки персональных данных и меры по обеспечению безопасности персональных данных, предпринимаемые самозанятым Засориным Кириллом Глебовичем ИНН 7710140679 (далее – Оператор).
            </p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">
              1.1. Оператор ставит своей важнейшей целью и условием осуществления своей деятельности соблюдение прав и свобод человека и гражданина при обработке его персональных данных, в том числе защиты прав на неприкосновенность частной жизни, личную и семейную тайну.
            </p>
            <p class="text-gray-700 dark:text-gray-300">
              1.2. Настоящая политика Оператора в отношении обработки персональных данных (далее – Политика) применяется ко всей информации, которую Оператор может получить о посетителях веб-сайта https://bot.revive-it.ru.
            </p>
          </div>

          <div class="mb-8">
            <h3 class="text-xl font-semibold text-secondary dark:text-light mb-4">2. Основные понятия, используемые в Политике</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.1. Автоматизированная обработка персональных данных – обработка персональных данных с помощью средств вычислительной техники;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.2. Блокирование персональных данных – временное прекращение обработки персональных данных (за исключением случаев, если обработка необходима для уточнения персональных данных);</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.3. Веб-сайт – совокупность графических и информационных материалов, а также программ для ЭВМ и баз данных, обеспечивающих их доступность в сети интернет по сетевому адресу https://bot.revive-it.ru;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.4. Информационная система персональных данных — совокупность содержащихся в базах данных персональных данных, и обеспечивающих их обработку информационных технологий и технических средств;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.5. Обезличивание персональных данных — действия, в результате которых невозможно определить без использования дополнительной информации принадлежность персональных данных конкретному Пользователю или иному субъекту персональных данных;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.6. Обработка персональных данных – любое действие (операция) или совокупность действий (операций), совершаемых с использованием средств автоматизации или без использования таких средств с персональными данными, включая сбор, запись, систематизацию, накопление, хранение, уточнение (обновление, изменение), извлечение, использование, передачу (распространение, предоставление, доступ), обезличивание, блокирование, удаление, уничтожение персональных данных;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.7. Оператор – государственный орган, муниципальный орган, юридическое или физическое лицо, самостоятельно или совместно с другими лицами организующие и (или) осуществляющие обработку персональных данных, а также определяющие цели обработки персональных данных, состав персональных данных, подлежащих обработке, действия (операции), совершаемые с персональными данными;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.8. Персональные данные – любая информация, относящаяся прямо или косвенно к определенному или определяемому Пользователю веб-сайта https://bot.revive-it.ru;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.9. Пользователь – любой посетитель веб-сайта https://bot.revive-it.ru;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.10. Предоставление персональных данных – действия, направленные на раскрытие персональных данных определенному лицу или определенному кругу лиц;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.11. Распространение персональных данных – любые действия, направленные на раскрытие персональных данных неопределенному кругу лиц (передача персональных данных) или на ознакомление с персональными данными неограниченного круга лиц, в том числе обнародование персональных данных в средствах массовой информации, размещение в информационно-телекоммуникационных сетях или предоставление доступа к персональным данным каким-либо иным способом;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">2.12. Трансграничная передача персональных данных – передача персональных данных на территорию иностранного государства органу власти иностранного государства, иностранному физическому или иностранному юридическому лицу;</p>
            <p class="text-gray-700 dark:text-gray-300">2.13. Уничтожение персональных данных – любые действия, в результате которых персональные данные уничтожаются безвозвратно с невозможностью дальнейшего восстановления содержания персональных данных в информационной системе персональных данных и (или) уничтожаются материальные носители персональных данных.</p>
          </div>
          <div class="mb-8">
            <h3 class="text-xl font-semibold text-secondary dark:text-light mb-4">3. Оператор может обрабатывать следующие персональные данные Пользователя</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-4">3.1. Электронный адрес;</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">3.2. Номера телефонов;</p>
            <p class="text-gray-700 dark:text-gray-300">3.4. Вышеперечисленные данные далее по тексту Политики объединены общим понятием Персональные данные</p>
          </div>

          <div class="mb-8">
            <h3 class="text-xl font-semibold text-secondary dark:text-light mb-4">4. Цели обработки персональных данных</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-4">4.1. Цель обработки персональных данных Пользователя — информирование Пользователя посредством отправки электронных писем.</p>
          </div>

          <div class="mb-8">
            <h3 class="text-xl font-semibold text-secondary dark:text-light mb-4">5. Правовые основания обработки персональных данных</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-4">5.1. Оператор обрабатывает персональные данные Пользователя только в случае их заполнения и/или отправки Пользователем самостоятельно через специальные формы, расположенные на сайте https://bot.revive-it.ru. Заполняя соответствующие формы и/или отправляя свои персональные данные Оператору, Пользователь выражает свое согласие с данной Политикой.</p>
            <p class="text-gray-700 dark:text-gray-300">5.2. Оператор обрабатывает обезличенные данные о Пользователе в случае, если это разрешено в настройках браузера Пользователя (включено сохранение файлов «cookie» и использование технологии JavaScript).</p>
          </div>

          <div class="mb-8">
            <h3 class="text-xl font-semibold text-secondary dark:text-light mb-4">6. Порядок сбора, хранения, передачи и других видов обработки персональных данных</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-4">Безопасность персональных данных, которые обрабатываются Оператором, обеспечивается путем реализации правовых, организационных и технических мер, необходимых для выполнения в полном объеме требований действующего законодательства в области защиты персональных данных.</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">6.1. Оператор обеспечивает сохранность персональных данных и принимает все возможные меры, исключающие доступ к персональным данным неуполномоченных лиц.</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">6.2. Персональные данные Пользователя никогда, ни при каких условиях не будут переданы третьим лицам, за исключением случаев, связанных с исполнением действующего законодательства.</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">6.3. В случае выявления неточностей в персональных данных, Пользователь может актуализировать их самостоятельно, путем направления Оператору уведомление на адрес электронной почты Оператора с пометкой «Актуализация персональных данных».</p>
            <p class="text-gray-700 dark:text-gray-300">6.4. Срок обработки персональных данных является неограниченным. Пользователь может в любой момент отозвать свое согласие на обработку персональных данных, направив Оператору уведомление посредством электронной почты на электронный адрес Оператора с пометкой «Отзыв согласия на обработку персональных данных».</p>
          </div>

          <div class="mb-8">
            <h3 class="text-xl font-semibold text-secondary dark:text-light mb-4">7. Трансграничная передача персональных данных</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-4">7.1. Оператор до начала осуществления трансграничной передачи персональных данных обязан убедиться в том, что иностранным государством, на территорию которого предполагается осуществлять передачу персональных данных, обеспечивается надежная защита прав субъектов персональных данных.</p>
            <p class="text-gray-700 dark:text-gray-300">7.2. Трансграничная передача персональных данных на территории иностранных государств, не отвечающих вышеуказанным требованиям, может осуществляться только в случае наличия согласия в письменной форме субъекта персональных данных на трансграничную передачу его персональных данных и/или исполнения договора, стороной которого является субъект персональных данных.</p>
          </div>

          <div class="mb-8">
            <h3 class="text-xl font-semibold text-secondary dark:text-light mb-4">8. Заключительные положения</h3>
            <p class="text-gray-700 dark:text-gray-300 mb-4">8.1. Вы можете отозвать своё согласие, написав на <EMAIL> в теме письма указав "Отзыв согасия на обработку ПД" доступ к сайту будет заблокирован, так как без этого согласия мы не можем предоставлять к нему доступ.</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">8.2. Пользователь может получить любые разъяснения по интересующим вопросам, касающимся обработки его персональных данных, обратившись к Оператору с помощью электронной почты.</p>
            <p class="text-gray-700 dark:text-gray-300 mb-4">8.3. В данном документе будут отражены любые изменения политики обработки персональных данных Оператором. Политика действует бессрочно до замены ее новой версией.</p>
            <p class="text-gray-700 dark:text-gray-300">8.4. Актуальная версия Политики в свободном доступе расположена в сети Интернет по адресу https://bot.revive-it.ru/privacy.html.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-secondary dark:bg-secondary-dark text-white py-16 transition-colors duration-300" id="contact">
    <div class="container">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-10 mb-8 md:mb-10">
        <div data-aos="fade-up" data-aos-delay="100">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Telegram Support Bot
          </h3>
          <p class="mb-6">
            Профессиональное решение для поддержки клиентов в Telegram с
            удобным интерфейсом для операторов и пользователей.
          </p>

        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Меню
          </h3>
          <ul class="space-y-3">
            <li>
              <a href="index.html#why-us" class="text-gray-300 hover:text-white transition-colors">Почему Мы</a>
            </li>
            <li>
              <a href="index.html#features" class="text-gray-300 hover:text-white transition-colors">Возможности</a>
            </li>
            <li>
              <a href="index.html#screenshots" class="text-gray-300 hover:text-white transition-colors">Скриншоты</a>
            </li>
            <li>
              <a href="index.html#pricing" class="text-gray-300 hover:text-white transition-colors">Тарифы</a>
            </li>
            <li>
              <a href="docs.html" class="text-gray-300 hover:text-white transition-colors">Документация</a>
            </li>
          </ul>
        </div>

        <div data-aos="fade-up" data-aos-delay="300">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Поддержка
          </h3>
          <ul class="space-y-3">
            <li>
              <a href="docs.html#faq" class="text-gray-300 hover:text-white transition-colors">FAQ</a>
            </li>
            <li>
              <a href="javascript:void(0)" class="text-gray-300 hover:text-white transition-colors">Контакты</a>
            </li>
            <li>
              <a href="privacy.html" class="text-white hover:text-accent transition-colors">Политика
                конфиденциальности</a>
            </li>
            <li>
              <a href="terms.html" class="text-gray-300 hover:text-white transition-colors">Условия
                использования</a>
            </li>
          </ul>
        </div>

        <div data-aos="fade-up" data-aos-delay="400">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Контакты
          </h3>
          <ul class="space-y-3">
            <li class="flex items-center space-x-2">
              <i class="fas fa-envelope"></i>
              <a href="mailto:<EMAIL>"
                class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
            </li>
            <li class="flex items-center space-x-2">
              <i class="fab fa-telegram"></i>
              <a href="https://t.me/rev_support_bot?start=bot_revive" target="_blank" class="text-gray-300 hover:text-white transition-colors">Наш бот в Telegram</a>
            </li>
          </ul>
        </div>
      </div>

      <div class="text-center pt-8 border-t border-gray-800">
        <div class="flex items-center justify-center mb-4">
          <p class="text-gray-300 text-base sm:text-base text-sm flex flex-wrap sm:flex-nowrap items-center justify-center">
            Сделано с <span class="text-red-500 mx-1 text-lg sm:text-lg text-base">❤️</span> в
            <a href="https://revive-it.ru" target="_blank" class="flex items-center ml-2">
              <img src="assets/img/revive-it-logo.svg" class="img-responsive sm:h-[30px] h-[24px]" alt="Revive-IT" style="width: auto; vertical-align: middle;">
            </a>
          </p>
        </div>
        <p class="text-gray-400 text-sm">
          &copy; 2025 Telegram Support Bot. Все права защищены.
        </p>
        <p class="text-gray-500 text-xs mt-2">
          Версия: <span id="app-version">1.0.1</span>
        </p>
      </div>
    </div>
  </footer>

  <!-- Модальное окно согласия с политикой конфиденциальности -->
  <div id="privacyConsentModal"
    class="fixed inset-0 z-[60] hidden bg-black bg-opacity-90 flex flex-col justify-center items-center p-4">
    <div class="bg-white dark:bg-dark-dark rounded-xl max-w-lg w-full p-6 relative shadow-xl">
      <div class="text-center mb-6">
        <h3 class="text-2xl font-bold text-secondary dark:text-light mb-4">Согласие на обработку персональных данных</h3>
        <p class="text-gray-600 dark:text-gray-300 text-left mb-4">
          Для улучшения качества наших услуг и предоставления вам актуальной информации о продукте, мы просим ваше согласие на обработку персональных данных.
        </p>
      </div>

      <div class="mb-6">
        <label class="flex items-start space-x-3 cursor-pointer">
          <input type="checkbox" id="privacyConsentCheckbox"
            class="mt-1 h-4 w-4 text-primary dark:text-primary-dark border-gray-300 dark:border-gray-600 rounded focus:ring-primary dark:focus:ring-primary-dark focus:ring-2">
          <span class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            Согласен(на) на получение информационных материалов от сайта по email или мессенджеру Telegram в соответствии с
            <a href="privacy.html" target="_blank" class="text-primary dark:text-primary-dark hover:underline">Политикой конфиденциальности</a>
          </span>
        </label>
      </div>

      <div class="flex flex-col sm:flex-row gap-3">
        <button id="privacyConsentAccept"
          class="flex-1 bg-primary dark:bg-primary-dark text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-dark dark:hover:bg-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled>
          Согласиться
        </button>
        <button id="privacyConsentDecline"
          class="flex-1 bg-gray-500 dark:bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-600 dark:hover:bg-gray-700 transition-colors duration-200">
          Отказаться
        </button>
      </div>

      <p class="text-xs text-gray-500 dark:text-gray-400 mt-4 text-center">
        При отказе от согласия функциональность сайта не ограничивается
      </p>
    </div>
  </div>
</body>

</html>
