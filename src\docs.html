<!DOCTYPE html>
<html lang="ru">

<head>
  <!-- Предотвращение мигания темы -->
  <script type="text/javascript">
    (function () {
      // Добавляем класс preload для предотвращения мигания
      document.documentElement.classList.add("preload");

      // Проверяем сохраненную тему или системные настройки
      var savedTheme = localStorage.getItem("theme");
      var prefersDark =
        window.matchMedia &&
        window.matchMedia("(prefers-color-scheme: dark)").matches;

      // Устанавливаем тему до загрузки основного контента
      if (savedTheme === "dark" || (!savedTheme && prefersDark)) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }

      // Удаляем класс preload после загрузки страницы
      window.addEventListener("load", function () {
        document.documentElement.classList.remove("preload");
      });
    })();
  </script>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- Мета-теги для управления кэшированием -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>Документация - Telegram Support Bot</title>
  <!-- Стили -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
  <link rel="stylesheet" href="assets/css/fonts.css" />

  <!-- Скрипты -->
  <!-- Скрипты загружаются автоматически через webpack -->
</head>

<body class="bg-light dark:bg-dark-dark">
  <!-- Header -->
  <header class="bg-white dark:bg-dark shadow-md fixed w-full top-0 z-50 transition-colors duration-300">
    <div class="container">
      <nav class="flex justify-between items-center py-5">
        <div id="logo" class="mr-3">
          <a href="index.html" class="logo-link flex flex-col items-center">
            <img src="assets/img/revive-it-logo-secondary.svg" class="img-responsive mb-1 sm:h-[32px] h-[28px] block dark:hidden" alt="Revive-IT" style="width: auto;">
            <img src="assets/img/revive-it-logo.svg" class="img-responsive mb-1 sm:h-[32px] h-[28px] hidden dark:block" alt="Revive-IT" style="width: auto;">
            <span class="text-xs sm:text-sm font-medium text-primary dark:text-primary-dark">Telegram Support Bot</span>
          </a>
        </div>
        <ul class="hidden md:flex space-x-8">
          <li>
            <a href="index.html#why-us"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Почему Мы</a>
          </li>
          <li>
            <a href="index.html#features"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Возможности</a>
          </li>
          <li>
            <a href="index.html#screenshots"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Скриншоты</a>
          </li>
          <li>
            <a href="index.html#pricing"
              class="font-medium text-dark dark:text-light hover:text-primary dark:hover:text-primary-dark transition-colors text-base tracking-tight">Тарифы</a>
          </li>
          <li>
            <a href="docs.html"
              class="font-medium text-primary dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary transition-colors text-base tracking-tight">Документация</a>
          </li>
        </ul>

        <!-- Переключатель темы -->
        <div class="flex items-center space-x-4">
          <button id="theme-toggle" class="theme-switch">
            <span class="sr-only">Переключить тему</span>
            <span class="theme-switch-track">
              <span class="theme-switch-thumb">
                <svg class="theme-switch-icon-sun" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                  fill="currentColor" width="16" height="16">
                  <path
                    d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                </svg>
                <svg class="theme-switch-icon-moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                  fill="currentColor" width="16" height="16">
                  <path fill-rule="evenodd"
                    d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"
                    clip-rule="evenodd" />
                </svg>
              </span>
            </span>
          </button>

          <button
            class="md:hidden cursor-pointer mobile-menu-button p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark">
            <span class="sr-only">Открыть меню</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-dark dark:text-light" fill="none"
              viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"
                class="menu-icon-bar" />
            </svg>
          </button>
        </div>
      </nav>
    </div>
  </header>

  <!-- Мобильное меню -->
  <div
    class="nav-links hidden md:hidden fixed top-[80px] left-0 w-full bg-white dark:bg-dark-dark shadow-lg z-40 transition-all duration-300 border-t border-gray-200 dark:border-gray-700 mobile-menu-container">
    <div class="container mx-auto pt-6 pb-4 px-4">
      <div class="grid gap-3">
        <a href="index.html#why-us" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
              clip-rule="evenodd" />
          </svg>
          Почему Мы
        </a>
        <a href="index.html#features" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
              clip-rule="evenodd" />
          </svg>
          Возможности
        </a>
        <a href="index.html#screenshots" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clip-rule="evenodd" />
          </svg>
          Скриншоты
        </a>
        <a href="index.html#pricing" class="mobile-menu-item">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path
              d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
              clip-rule="evenodd" />
          </svg>
          Тарифы
        </a>
        <a href="docs.html" class="mobile-menu-item active">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
          </svg>
          Документация
        </a>
      </div>
    </div>
  </div>

  <!-- Основное содержимое страницы документации -->
  <section class="pt-32 pb-24 bg-gradient-to-br from-light to-blue-50 dark:from-dark-dark dark:to-dark transition-colors duration-300">
    <div class="container" data-aos="fade-up">
      <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-secondary dark:text-light mb-4 md:mb-6 tracking-tight leading-tight text-center">
        Документация
      </h1>
      <p class="text-base sm:text-lg md:text-xl text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mb-8 md:mb-10 leading-relaxed text-center">
        Техническая документация и файлы для скачивания
      </p>
    </div>
  </section>

  <!-- Секция загрузки файлов -->
  <section id="downloads" class="section bg-white dark:bg-dark-dark transition-colors duration-300">
    <div class="container">
      <div class="section-title" data-aos="fade-up">
        <h2>Файлы для скачивания</h2>
        <p>Здесь вы можете скачать исполняемые файлы Telegram Support Bot для различных операционных систем</p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-8 sm:gap-6 md:gap-8 max-w-4xl mx-auto">
        <!-- Windows -->
        <div class="download-card h-full" data-aos="fade-up" data-aos-delay="100">
          <div class="coming-soon-label"></div>
          <div class="download-icon">
            <i class="fab fa-windows text-blue-500 dark:text-blue-400"></i>
          </div>
          <h3>Windows</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Версия для Windows 8, 10, 11 (x64)
          </p>
          <button class="btn btn-sm mt-auto disabled" disabled>
            <i class="fas fa-download mr-2"></i> Скачать для Windows
          </button>
        </div>

        <!-- macOS -->
        <div class="download-card h-full" data-aos="fade-up" data-aos-delay="200">
          <div class="coming-soon-label"></div>
          <div class="download-icon">
            <i class="fab fa-apple text-gray-800 dark:text-gray-200"></i>
          </div>
          <h3>macOS</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Версия для macOS 10.13 и выше (Intel и Apple Silicon)
          </p>
          <button class="btn btn-sm mt-auto disabled" disabled>
            <i class="fas fa-download mr-2"></i> Скачать для macOS
          </button>
        </div>

        <!-- Linux -->
        <div class="download-card h-full" data-aos="fade-up" data-aos-delay="300">
          <div class="coming-soon-label"></div>
          <div class="download-icon">
            <i class="fab fa-linux text-orange-500 dark:text-orange-400"></i>
          </div>
          <h3>Linux</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Версия для Linux (x64, ARM64)
          </p>
          <div class="mt-auto space-y-2">
            <button class="btn btn-sm disabled" disabled>
              <i class="fas fa-download mr-2"></i> Скачать для Linux (x64)
            </button>
            <button class="btn btn-sm disabled" disabled>
              <i class="fas fa-download mr-2"></i> Скачать для Linux (ARM64)
            </button>
          </div>
        </div>

        <!-- FreeBSD -->
        <div class="download-card h-full" data-aos="fade-up" data-aos-delay="400">
          <div class="coming-soon-label"></div>
          <div class="download-icon">
            <i class="fab fa-freebsd text-red-500 dark:text-red-400"></i>
          </div>
          <h3>FreeBSD</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            Версия для FreeBSD 12 и выше (x64, ARM64)
          </p>
          <div class="mt-auto space-y-2">
            <button class="btn btn-sm disabled" disabled>
              <i class="fas fa-download mr-2"></i> Скачать для FreeBSD (x64)
            </button>
            <button class="btn btn-sm disabled" disabled>
              <i class="fas fa-download mr-2"></i> Скачать для FreeBSD (ARM64)
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Секция документации -->
  <section class="section bg-blue-50 dark:bg-dark transition-colors duration-300">
    <div class="container">
      <div class="section-title" data-aos="fade-up">
        <h2>Руководство пользователя</h2>
        <p>Подробная документация по установке и использованию Telegram Support Bot</p>
      </div>

      <div class="docs-content" data-aos="fade-up">
        <div class="bg-white dark:bg-dark-dark p-6 rounded-lg shadow-lg">
          <h3 class="text-2xl font-bold text-secondary dark:text-light mb-6">Установка и настройка</h3>

          <div class="mb-8">
            <h4 class="text-xl font-semibold text-secondary dark:text-light mb-4">Системные требования</h4>
            <ul class="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
              <li>Операционная система: Windows 8+, macOS 10.13+, Linux (любой современный дистрибутив)</li>
              <li>Оперативная память: минимум 512 МБ</li>
              <li>Процессор: любой современный (x86, x64, ARM)</li>
              <li>Дисковое пространство: минимум 50 МБ</li>
              <li>Интернет-соединение: постоянное подключение к интернету</li>
            </ul>
          </div>

          <div class="mb-8">
            <h4 class="text-xl font-semibold text-secondary dark:text-light mb-4">Пошаговая инструкция по установке</h4>
            <ol class="list-decimal pl-6 space-y-4 text-gray-700 dark:text-gray-300">
              <li>
                <strong>Скачайте файл</strong> для вашей операционной системы из раздела "Файлы для скачивания".
              </li>
              <li>
                <strong>Распакуйте архив</strong> в любую удобную директорию на вашем компьютере.
              </li>
              <li>
                <strong>Запустите исполняемый файл</strong>:
                <ul class="list-disc pl-6 mt-2 space-y-1">
                  <li>Windows: запустите файл <code>telegram-support-bot.exe</code></li>
                  <li>macOS: откройте файл <code>telegram-support-bot</code></li>
                  <li>Linux: запустите файл <code>telegram-support-bot</code> через терминал</li>
                </ul>
              </li>
              <li>
                <strong>Следуйте инструкциям</strong> в консоли для первоначальной настройки бота.
              </li>
              <li>
                <strong>Создайте бота</strong> в Telegram через @BotFather и получите API-токен.
              </li>
              <li>
                <strong>Создайте файл конфигурации</strong> из примера:
                <ul class="list-disc pl-6 mt-2 space-y-1">
                  <li>Выполните команду: <code>cp .env.sample .env</code></li>
                  <li>Откройте файл <code>.env</code> в любом текстовом редакторе</li>
                </ul>
              </li>
              <li>
                <strong>Введите API-токен</strong> в файл <code>.env</code> в поле <code>BOT_TOKEN=</code>
              </li>
              <li>
                <strong>Настройте параметры</strong> бота согласно вашим потребностям.
              </li>
            </ol>
          </div>

          <div class="mb-8">
            <h4 class="text-xl font-semibold text-secondary dark:text-light mb-4">Настройка автозапуска</h4>
            <div class="text-gray-700 dark:text-gray-300">
              <p class="mb-4">Для настройки автоматического запуска бота при старте системы:</p>

              <h5 class="font-semibold mb-2">Windows:</h5>
              <ol class="list-decimal pl-6 mb-4 space-y-1">
                <li>Создайте ярлык исполняемого файла</li>
                <li>Нажмите Win+R, введите <code>shell:startup</code> и нажмите Enter</li>
                <li>Переместите созданный ярлык в открывшуюся папку</li>
              </ol>

              <h5 class="font-semibold mb-2">macOS:</h5>
              <ol class="list-decimal pl-6 mb-4 space-y-1">
                <li>Откройте "Системные настройки" > "Пользователи и группы"</li>
                <li>Выберите вкладку "Объекты входа"</li>
                <li>Нажмите "+" и добавьте приложение Telegram Support Bot</li>
              </ol>

              <h5 class="font-semibold mb-2">Linux:</h5>
              <ol class="list-decimal pl-6 space-y-1">
                <li>Создайте файл <code>telegram-support-bot.service</code> в директории <code>~/.config/systemd/user/</code></li>
                <li>Добавьте в файл конфигурацию службы systemd</li>
                <li>Выполните команду <code>systemctl --user enable telegram-support-bot.service</code></li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Секция FAQ -->
  <section class="section bg-white dark:bg-dark-dark transition-colors duration-300" id="faq">
    <div class="container">
      <div class="section-title" data-aos="fade-up">
        <h2>Часто задаваемые вопросы</h2>
        <p>Ответы на распространенные вопросы о Telegram Support Bot</p>
      </div>

      <div class="faq-content" data-aos="fade-up">
        <div class="space-y-6">
          <div class="faq-item">
            <h4 class="faq-question">Что представляет из себя Телеграм бот?</h4>
            <div class="faq-answer">
              <p>Telegram Support Bot — это специализированное программное решение для организации службы поддержки клиентов через мессенджер Telegram. Наш бот разработан на языке Go, что обеспечивает высокую производительность, минимальное потребление ресурсов и кроссплатформенность.</p>
              <p class="mt-2">Технически бот представляет собой:</p>
              <ul class="list-disc pl-6 mt-2 space-y-1">
                <li>Единый исполняемый файл без внешних зависимостей</li>
                <li>Встроенную базу данных SQLite для хранения истории сообщений и настроек</li>
                <li>Клиент-серверное приложение, где серверная часть взаимодействует с API Telegram</li>
                <li>Интерфейс оператора, доступный через Telegram</li>
              </ul>
              <p class="mt-2">Бот использует официальный Telegram Bot API для обмена сообщениями, что обеспечивает надежность и безопасность коммуникаций. Все данные хранятся локально на вашем компьютере или сервере, что дает полный контроль над информацией.</p>
            </div>
          </div>

          <div class="faq-item">
            <h4 class="faq-question">Как подключить нескольких операторов к боту?</h4>
            <div class="faq-answer">
              <p>Для подключения нескольких операторов выполните следующие шаги:</p>
              <ol class="list-decimal pl-6 mt-2 space-y-1">
                <li>Создайте из файла <code>.env.sample</code> файл <code>.env</code></li>
                <li>Выполните команду: <code>cp .env.sample .env</code></li>
                <li>Откройте <code>.env</code> в любимом редакторе</li>
                <li>в OPERATORS= пречислите имена операторов разделяя запятой</li>
                <li>Вводите Telegram <code>@nickname</code> нового оператора</li>
                <li>Сохраните файл и перезапустите бота</li>
              </ol>
            </div>
          </div>

          <div class="faq-item">
            <h4 class="faq-question">Как настроить автоматические ответы на типовые вопросы?</h4>
            <div class="faq-answer">
              <p>Для настройки автоматических ответов:</p>
              <ol class="list-decimal pl-6 mt-2 space-y-1">
                <li>Откройте раздел "Шаблоны ответов"</li>
                <li>Нажмите "Создать новый шаблон"</li>
                <li>Введите ключевые слова, которые должны содержаться в вопросе клиента</li>
                <li>Составьте текст автоматического ответа</li>
              </ol>
            </div>
          </div>

          <div class="faq-item">
            <h4 class="faq-question">Как настроить резервное копирование данных?</h4>
            <div class="faq-answer">
              <p>Для настройки автоматического резервного копирования:</p>
              <ol class="list-decimal pl-6 mt-2 space-y-1">
                <li>Нажмите "Бэкап" в интерфейсе оператора</li>
                <li>Включите опцию "Автоматическое резервное копирование"</li>
                <li>Установите периодичность создания резервных копий</li>
                <li>Список резервных копий хранит все ваши резервные копии на сервере</li>
              </ol>
            </div>
          </div>

          <div class="faq-item">
            <h4 class="faq-question">Как отслеживать эффективность работы операторов?</h4>
            <div class="faq-answer">
              <p>Для отслеживания эффективности работы операторов:</p>
              <ol class="list-decimal pl-6 mt-2 space-y-1">
                <li>Откройте раздел "Статистика" в меню Оператора</li>
                <li>Просмотрите данные по каждому оператору: количество обработанных запросов, среднее время ответа, рейтинг удовлетворенности клиентов</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-secondary dark:bg-secondary-dark text-white py-16 transition-colors duration-300" id="contact">
    <div class="container">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-10 mb-8 md:mb-10">
        <div data-aos="fade-up" data-aos-delay="100">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Telegram Support Bot
          </h3>
          <p class="mb-6">
            Профессиональное решение для поддержки клиентов в Telegram с
            удобным интерфейсом для операторов и пользователей.
          </p>

        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Меню
          </h3>
          <ul class="space-y-3">
            <li>
              <a href="index.html#why-us" class="text-gray-300 hover:text-white transition-colors">Почему Мы</a>
            </li>
            <li>
              <a href="index.html#features" class="text-gray-300 hover:text-white transition-colors">Возможности</a>
            </li>
            <li>
              <a href="index.html#screenshots" class="text-gray-300 hover:text-white transition-colors">Скриншоты</a>
            </li>
            <li>
              <a href="index.html#pricing" class="text-gray-300 hover:text-white transition-colors">Тарифы</a>
            </li>
            <li>
              <a href="docs.html" class="text-white hover:text-accent transition-colors">Документация</a>
            </li>
          </ul>
        </div>

        <div data-aos="fade-up" data-aos-delay="300">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Поддержка
          </h3>
          <ul class="space-y-3">
            <li>
              <a href="docs.html#faq" class="text-gray-300 hover:text-white transition-colors">FAQ</a>
            </li>
            <li>
              <a href="javascript:void(0)" class="text-gray-300 hover:text-white transition-colors">Контакты</a>
            </li>
            <li>
              <a href="privacy.html" class="text-gray-300 hover:text-white transition-colors">Политика
                конфиденциальности</a>
            </li>
            <li>
              <a href="terms.html" class="text-gray-300 hover:text-white transition-colors">Условия
                использования</a>
            </li>
          </ul>
        </div>

        <div data-aos="fade-up" data-aos-delay="400">
          <h3
            class="text-lg font-semibold mb-6 relative pb-3 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-accent">
            Контакты
          </h3>
          <ul class="space-y-3">
            <li class="flex items-center space-x-2">
              <i class="fas fa-envelope"></i>
              <a href="mailto:<EMAIL>"
                class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
            </li>
            <li class="flex items-center space-x-2">
              <i class="fab fa-telegram"></i>
              <a href="https://t.me/rev_support_bot?start=bot_revive" target="_blank" class="text-gray-300 hover:text-white transition-colors">Наш бот в Telegram</a>
            </li>
          </ul>
        </div>
      </div>

      <div class="text-center pt-8 border-t border-gray-800">
        <div class="flex items-center justify-center mb-4">
          <p class="text-gray-300 text-base sm:text-base text-sm flex flex-wrap sm:flex-nowrap items-center justify-center">
            Сделано с <span class="text-red-500 mx-1 text-lg sm:text-lg text-base">❤️</span> в
            <a href="https://revive-it.ru" target="_blank" class="flex items-center ml-2">
              <img src="assets/img/revive-it-logo.svg" class="img-responsive sm:h-[30px] h-[24px]" alt="Revive-IT" style="width: auto; vertical-align: middle;">
            </a>
          </p>
        </div>
        <p class="text-gray-400 text-sm">
          &copy; 2025 Telegram Support Bot. Все права защищены.
        </p>
        <p class="text-gray-500 text-xs mt-2">
          Версия: <span id="app-version">1.0.1</span>
        </p>
      </div>
    </div>
  </footer>

  <!-- Модальное окно согласия с политикой конфиденциальности -->
  <div id="privacyConsentModal"
    class="fixed inset-0 z-[60] hidden bg-black bg-opacity-90 flex flex-col justify-center items-center p-4">
    <div class="bg-white dark:bg-dark-dark rounded-xl max-w-lg w-full p-6 relative shadow-xl">
      <div class="text-center mb-6">
        <h3 class="text-2xl font-bold text-secondary dark:text-light mb-4">Согласие на обработку персональных данных</h3>
        <p class="text-gray-600 dark:text-gray-300 text-left mb-4">
          Для улучшения качества наших услуг и предоставления вам актуальной информации о продукте, мы просим ваше согласие на обработку персональных данных.
        </p>
      </div>

      <div class="mb-6">
        <label class="flex items-start space-x-3 cursor-pointer">
          <input type="checkbox" id="privacyConsentCheckbox"
            class="mt-1 h-4 w-4 text-primary dark:text-primary-dark border-gray-300 dark:border-gray-600 rounded focus:ring-primary dark:focus:ring-primary-dark focus:ring-2">
          <span class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            Согласен(на) на получение информационных материалов от сайта по email или мессенджеру Telegram в соответствии с
            <a href="privacy.html" target="_blank" class="text-primary dark:text-primary-dark hover:underline">Политикой конфиденциальности</a>
          </span>
        </label>
      </div>

      <div class="flex flex-col sm:flex-row gap-3">
        <button id="privacyConsentAccept"
          class="flex-1 bg-primary dark:bg-primary-dark text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-dark dark:hover:bg-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled>
          Согласиться
        </button>
        <button id="privacyConsentDecline"
          class="flex-1 bg-gray-500 dark:bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-600 dark:hover:bg-gray-700 transition-colors duration-200">
          Отказаться
        </button>
      </div>

      <p class="text-xs text-gray-500 dark:text-gray-400 mt-4 text-center">
        При отказе от согласия функциональность сайта не ограничивается
      </p>
    </div>
  </div>
</body>

</html>
