<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест системы согласий</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Темная тема */
        .dark {
            background-color: #0a1428;
            color: #fff;
        }
        
        /* Стили для чекбокса */
        input[type="checkbox"]:checked {
            background-color: #3b82f6;
            border-color: #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen flex items-center justify-center">
    <div class="max-w-md mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Тест системы согласий</h1>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
            Нажмите кнопку ниже, чтобы протестировать модальное окно согласия с политикой конфиденциальности.
        </p>
        
        <div class="space-y-4">
            <button id="showConsentBtn"
                class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                Показать модальное окно согласия
            </button>

            <button id="clearConsentBtn"
                class="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                Очистить сохраненное согласие
            </button>

            <button id="exportLogsBtn"
                class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                Экспорт логов (JSON)
            </button>

            <button id="exportCSVBtn"
                class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                Экспорт логов (CSV)
            </button>

            <button id="showStatsBtn"
                class="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors">
                Показать статистику
            </button>

            <button id="toggleThemeBtn"
                class="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                Переключить тему
            </button>
        </div>
        
        <div id="status" class="mt-4 p-3 rounded-lg bg-gray-100 dark:bg-gray-700">
            <p class="text-sm text-gray-600 dark:text-gray-300">
                Статус: <span id="consentStatus">Проверка...</span>
            </p>
        </div>
    </div>

    <!-- Модальное окно согласия с политикой конфиденциальности -->
    <div id="privacyConsentModal"
        class="fixed inset-0 z-[60] hidden bg-black bg-opacity-90 flex flex-col justify-center items-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-xl max-w-lg w-full p-6 relative shadow-xl">
            <div class="text-center mb-6">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Согласие на обработку персональных данных</h3>
                <p class="text-gray-600 dark:text-gray-300 text-left mb-4">
                    Для улучшения качества наших услуг и предоставления вам актуальной информации о продукте, мы просим ваше согласие на обработку персональных данных.
                </p>
            </div>
            
            <div class="mb-6">
                <label class="flex items-start space-x-3 cursor-pointer">
                    <input type="checkbox" id="privacyConsentCheckbox" 
                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                    <span class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                        Согласен(на) на получение информационных материалов от сайта по email или мессенджеру Telegram в соответствии с 
                        <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">Политикой конфиденциальности</a>
                    </span>
                </label>
            </div>
            
            <div class="flex flex-col sm:flex-row gap-3">
                <button id="privacyConsentAccept" 
                    class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled>
                    Согласиться
                </button>
                <button id="privacyConsentDecline" 
                    class="flex-1 bg-gray-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-600 transition-colors duration-200">
                    Отказаться
                </button>
            </div>
            
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-4 text-center">
                При отказе от согласия функциональность сайта не ограничивается
            </p>
        </div>
    </div>

    <script>
        // Система согласия с политикой конфиденциальности
        const privacyConsentManager = {
            CONSENT_KEY: 'privacy_consent_given',
            CONSENT_DATA_KEY: 'privacy_consent_data',
            
            hasConsent: function() {
                return localStorage.getItem(this.CONSENT_KEY) === 'true';
            },
            
            getUserIP: async function() {
                try {
                    const response = await fetch('https://api.ipify.org?format=json');
                    const data = await response.json();
                    return data.ip;
                } catch (error) {
                    console.log('Не удалось получить IP адрес:', error);
                    return 'unknown';
                }
            },
            
            getSource: function() {
                return document.referrer || 'direct';
            },
            
            saveConsentData: async function(consent) {
                const ip = await this.getUserIP();
                const consentData = {
                    consent: consent,
                    ip: ip,
                    date: new Date().toISOString(),
                    source: this.getSource(),
                    userAgent: navigator.userAgent,
                    page: window.location.href
                };

                localStorage.setItem(this.CONSENT_KEY, consent.toString());
                localStorage.setItem(this.CONSENT_DATA_KEY, JSON.stringify(consentData));

                // Логируем данные локально
                this.logConsentLocally(consentData);

                console.log('Данные согласия сохранены:', consentData);
                this.updateStatus();

                return consentData;
            },

            logConsentLocally: function(consentData) {
                try {
                    const existingLogs = JSON.parse(localStorage.getItem('consent_logs') || '[]');

                    const logEntry = {
                        id: Date.now() + Math.random().toString(36).substr(2, 9),
                        timestamp: new Date().toISOString(),
                        ...consentData
                    };

                    existingLogs.push(logEntry);

                    if (existingLogs.length > 1000) {
                        existingLogs.splice(0, existingLogs.length - 1000);
                    }

                    localStorage.setItem('consent_logs', JSON.stringify(existingLogs));

                    console.log('Согласие зафиксировано локально:', {
                        consent: logEntry.consent,
                        ip: logEntry.ip,
                        date: logEntry.date,
                        source: logEntry.source
                    });

                } catch (error) {
                    console.error('Ошибка локального логирования:', error);
                }
            },
            
            showConsentModal: function() {
                const modal = document.getElementById('privacyConsentModal');
                if (modal) {
                    modal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');
                }
            },
            
            hideConsentModal: function() {
                const modal = document.getElementById('privacyConsentModal');
                if (modal) {
                    modal.classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                }
            },
            
            clearConsent: function() {
                localStorage.removeItem(this.CONSENT_KEY);
                localStorage.removeItem(this.CONSENT_DATA_KEY);
                this.updateStatus();
                console.log('Согласие очищено');
            },

            exportLogs: function() {
                try {
                    const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');
                    const dataStr = JSON.stringify(logs, null, 2);
                    const dataBlob = new Blob([dataStr], { type: 'application/json' });

                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(dataBlob);
                    link.download = `consent-logs-${new Date().toISOString().split('T')[0]}.json`;
                    link.click();

                    alert(`Экспортировано ${logs.length} записей в JSON`);
                } catch (error) {
                    console.error('Ошибка экспорта:', error);
                    alert('Ошибка экспорта данных');
                }
            },

            exportCSV: function() {
                try {
                    const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');

                    if (logs.length === 0) {
                        alert('Нет данных для экспорта');
                        return;
                    }

                    const BOM = '\uFEFF';
                    const headers = ['ID', 'Дата', 'Время', 'Согласие', 'IP', 'Источник', 'Страница'];
                    const csvRows = [headers.join(';')];

                    logs.forEach(log => {
                        const date = new Date(log.date);
                        const row = [
                            log.id,
                            date.toLocaleDateString('ru-RU'),
                            date.toLocaleTimeString('ru-RU'),
                            log.consent ? 'Согласие' : 'Отказ',
                            log.ip,
                            `"${log.source.replace(/"/g, '""')}"`,
                            `"${log.page.replace(/"/g, '""')}"`
                        ];
                        csvRows.push(row.join(';'));
                    });

                    const csvContent = BOM + csvRows.join('\n');
                    const dataBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(dataBlob);
                    link.download = `consent-logs-${new Date().toISOString().split('T')[0]}.csv`;
                    link.click();

                    alert(`Экспортировано ${logs.length} записей в CSV`);
                } catch (error) {
                    console.error('Ошибка экспорта CSV:', error);
                    alert('Ошибка экспорта CSV');
                }
            },

            showStats: function() {
                try {
                    const logs = JSON.parse(localStorage.getItem('consent_logs') || '[]');

                    const stats = {
                        total: logs.length,
                        consented: logs.filter(log => log.consent === true).length,
                        declined: logs.filter(log => log.consent === false).length,
                        today: logs.filter(log => {
                            const logDate = new Date(log.date);
                            const today = new Date();
                            return logDate.toDateString() === today.toDateString();
                        }).length
                    };

                    const message = `Статистика согласий:
Всего: ${stats.total}
Согласились: ${stats.consented}
Отказались: ${stats.declined}
Сегодня: ${stats.today}`;

                    alert(message);
                    console.table(stats);
                } catch (error) {
                    console.error('Ошибка получения статистики:', error);
                    alert('Ошибка получения статистики');
                }
            },
            
            updateStatus: function() {
                const statusElement = document.getElementById('consentStatus');
                if (statusElement) {
                    if (this.hasConsent()) {
                        const data = JSON.parse(localStorage.getItem(this.CONSENT_DATA_KEY) || '{}');
                        statusElement.textContent = `Согласие дано (${data.consent ? 'Принято' : 'Отклонено'}) - ${new Date(data.date).toLocaleString()}`;
                    } else {
                        statusElement.textContent = 'Согласие не дано';
                    }
                }
            },
            
            init: function() {
                this.updateStatus();
                this.initEventHandlers();
            },
            
            initEventHandlers: function() {
                const checkbox = document.getElementById('privacyConsentCheckbox');
                const acceptBtn = document.getElementById('privacyConsentAccept');
                const declineBtn = document.getElementById('privacyConsentDecline');
                const showBtn = document.getElementById('showConsentBtn');
                const clearBtn = document.getElementById('clearConsentBtn');
                const exportBtn = document.getElementById('exportLogsBtn');
                const exportCSVBtn = document.getElementById('exportCSVBtn');
                const statsBtn = document.getElementById('showStatsBtn');
                const themeBtn = document.getElementById('toggleThemeBtn');
                
                if (checkbox && acceptBtn) {
                    checkbox.addEventListener('change', function() {
                        acceptBtn.disabled = !this.checked;
                    });
                }
                
                if (acceptBtn) {
                    acceptBtn.addEventListener('click', async () => {
                        if (checkbox && checkbox.checked) {
                            await this.saveConsentData(true);
                            this.hideConsentModal();
                            alert('Согласие на обработку персональных данных получено');
                        }
                    });
                }
                
                if (declineBtn) {
                    declineBtn.addEventListener('click', async () => {
                        await this.saveConsentData(false);
                        this.hideConsentModal();
                        alert('Отказ от обработки персональных данных зафиксирован');
                    });
                }
                
                if (showBtn) {
                    showBtn.addEventListener('click', () => {
                        this.showConsentModal();
                    });
                }
                
                if (clearBtn) {
                    clearBtn.addEventListener('click', () => {
                        this.clearConsent();
                    });
                }

                if (exportBtn) {
                    exportBtn.addEventListener('click', () => {
                        this.exportLogs();
                    });
                }

                if (exportCSVBtn) {
                    exportCSVBtn.addEventListener('click', () => {
                        this.exportCSV();
                    });
                }

                if (statsBtn) {
                    statsBtn.addEventListener('click', () => {
                        this.showStats();
                    });
                }

                if (themeBtn) {
                    themeBtn.addEventListener('click', () => {
                        document.documentElement.classList.toggle('dark');
                    });
                }
            }
        };
        
        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', () => {
            privacyConsentManager.init();
        });
    </script>
</body>
</html>
