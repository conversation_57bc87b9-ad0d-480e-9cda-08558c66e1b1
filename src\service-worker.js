/**
 * Service Worker для управления кэшированием и обновлением сайта
 * Позволяет автоматически обновлять контент при изменении версии сайта
 */

// Версия кэша, которая будет обновляться при каждом деплое
const CACHE_VERSION = 'v1.2.6-20250710-436';
const CACHE_NAME = `telegram-bot-site-${CACHE_VERSION}`;

// Список ресурсов для предварительного кэширования
const urlsToCache = [
  '/',
  '/index.html',
  '/docs.html',
  '/privacy.html'
];

// Установка Service Worker и предварительное кэширование ресурсов
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
      .then(() => self.skipWaiting()) // Активирует новый SW сразу после установки
  );
});

// Активация Service Worker и удаление старых кэшей
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          // Удаляем все старые кэши, кроме текущего
          return cacheName.startsWith('telegram-bot-site-') && cacheName !== CACHE_NAME;
        }).map(cacheName => {
          console.log('Deleting old cache:', cacheName);
          return caches.delete(cacheName);
        })
      );
    }).then(() => self.clients.claim()) // Берем под контроль все открытые вкладки
  );
});

// Стратегия кэширования: сначала сеть, затем кэш
self.addEventListener('fetch', event => {
  // Пропускаем запросы к API или другим доменам
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Для HTML запросов всегда обращаемся к сети и обновляем кэш
  if (event.request.mode === 'navigate' ||
      (event.request.method === 'GET' &&
       event.request.headers.get('accept').includes('text/html'))) {
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Если получили успешный ответ, кэшируем его
          if (response.status === 200) {
            const responseToCache = response.clone();
            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              });
          }
          return response;
        })
        .catch(() => {
          // При ошибке сети возвращаем из кэша
          return caches.match(event.request);
        })
    );
    return;
  }

  // Для остальных запросов используем стратегию stale-while-revalidate
  event.respondWith(
    caches.match(event.request)
      .then(cachedResponse => {
        // Даже если есть кэшированный ответ, делаем запрос к сети для обновления кэша
        const fetchPromise = fetch(event.request)
          .then(networkResponse => {
            // Обновляем кэш только для успешных ответов
            if (networkResponse.ok) {
              // Клонируем ответ сразу, до использования тела
              const responseToCache = networkResponse.clone();
              caches.open(CACHE_NAME)
                .then(cache => {
                  cache.put(event.request, responseToCache);
                });
            }
            return networkResponse;
          })
          .catch(() => {
            // Если сеть недоступна, возвращаем null
            return null;
          });

        // Возвращаем кэшированный ответ или результат сетевого запроса
        return cachedResponse || fetchPromise;
      })
  );
});

// Обработка сообщений от клиента
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
